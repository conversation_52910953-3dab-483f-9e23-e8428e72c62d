==================== START: ai-agent-integration-tmpl ====================
# AI Agent Integration Specification: {Agent Name}
## Agentic AI Capabilities and Orchestration Framework

### Document Information
- **Agent Name:** {Agent Name}
- **Agent Type:** {Conversational/Automation/Analytics/Orchestration}
- **Document Version:** 1.0
- **Creation Date:** {Date}
- **Owner Team:** {Team Name}
- **Last Updated:** {Date}

---

## 1. Agent Overview and Purpose

### Business Purpose
{Clear description of the business value and objectives this AI agent addresses}

### Agent Classification
- [ ] **Conversational Agent** - Natural language interaction and customer service
- [ ] **Automation Agent** - Task automation and workflow execution
- [ ] **Analytics Agent** - Data analysis and insights generation
- [ ] **Orchestration Agent** - Multi-agent coordination and workflow management
- [ ] **Decision Agent** - Automated decision-making and recommendation
- [ ] **Monitoring Agent** - System monitoring and anomaly detection

### Core Capabilities
{List the primary capabilities and functions this agent will perform}

### Success Metrics
{Define measurable outcomes and KPIs for agent performance}

---

## 2. Agent Architecture and Design

### Agent Framework
- **Base Framework:** {LangChain/LangGraph/Custom/Other}
- **Model Provider:** {OpenAI/Anthropic/Azure/AWS/Local}
- **Model Specifications:** {Model name, version, and configuration}
- **Context Window:** {Token limits and context management strategy}

### Agent Components
```yaml
Agent Architecture:
  Core Engine:
    - Reasoning Module: {Decision-making and planning capabilities}
    - Memory System: {Short-term and long-term memory management}
    - Tool Integration: {External tool and API access}
    - Safety Controls: {Guardrails and safety mechanisms}
  
  Communication Layer:
    - Input Processing: {Request parsing and validation}
    - Output Generation: {Response formatting and delivery}
    - Protocol Support: {REST API, WebSocket, gRPC, etc.}
    - Authentication: {Security and access control}
  
  Integration Layer:
    - Service Connectors: {Microservice integration points}
    - Data Access: {Database and data source connections}
    - Event Handling: {Event-driven communication}
    - Monitoring: {Performance and health monitoring}
```

### Memory and Context Management
- **Short-term Memory:** {Session-based context and conversation history}
- **Long-term Memory:** {Persistent knowledge and learning storage}
- **Context Retrieval:** {Vector database and semantic search capabilities}
- **Memory Persistence:** {Storage strategy and data lifecycle}

---

## 3. Human-AI Collaboration Framework

### Collaboration Patterns
- **Human-in-the-Loop:** {When and how humans are involved in agent decisions}
- **Human-on-the-Loop:** {Human oversight and monitoring procedures}
- **Human-out-of-the-Loop:** {Fully autonomous operation scenarios}

### Handoff Procedures
```yaml
Human-AI Handoff Scenarios:
  Agent to Human:
    - Trigger Conditions: {When agent escalates to human}
    - Context Transfer: {How context is preserved during handoff}
    - Notification Method: {How humans are alerted}
    - Response Time SLA: {Expected human response time}
  
  Human to Agent:
    - Delegation Criteria: {When humans delegate to agent}
    - Instruction Format: {How humans provide instructions}
    - Confirmation Process: {Agent acknowledgment and validation}
    - Monitoring Requirements: {Human oversight during execution}
```

### Escalation Protocols
- **Complexity Escalation:** {When tasks exceed agent capabilities}
- **Error Escalation:** {When agent encounters errors or failures}
- **Policy Escalation:** {When decisions require human approval}
- **Emergency Escalation:** {Critical situations requiring immediate human intervention}

---

## 4. Multi-Agent Orchestration

### Agent Relationships
```yaml
Agent Ecosystem:
  Upstream Agents:
    - {Agent Name}: {Relationship type and communication pattern}
    - {Agent Name}: {Relationship type and communication pattern}
  
  Downstream Agents:
    - {Agent Name}: {Relationship type and communication pattern}
    - {Agent Name}: {Relationship type and communication pattern}
  
  Peer Agents:
    - {Agent Name}: {Collaboration pattern and shared responsibilities}
    - {Agent Name}: {Collaboration pattern and shared responsibilities}
```

### Coordination Patterns
- **Sequential Workflow:** {Step-by-step agent coordination}
- **Parallel Processing:** {Concurrent agent execution}
- **Hierarchical Coordination:** {Master-worker agent patterns}
- **Peer-to-Peer Collaboration:** {Distributed agent coordination}

### Communication Protocols
- **Message Format:** {Standardized message structure for inter-agent communication}
- **Event Schema:** {Event-driven communication patterns}
- **Synchronization:** {Coordination and synchronization mechanisms}
- **Conflict Resolution:** {Handling conflicting agent decisions}

---

## 5. Service Integration and APIs

### Microservice Integration
```yaml
Service Dependencies:
  Required Services:
    - {Service Name}: {Integration purpose and API endpoints}
    - {Service Name}: {Integration purpose and API endpoints}
  
  Optional Services:
    - {Service Name}: {Enhanced functionality and fallback behavior}
    - {Service Name}: {Enhanced functionality and fallback behavior}
```

### API Specifications
```yaml
Agent API Endpoints:
  POST /api/v1/agents/{agent-id}/execute:
    description: Execute agent task with specified parameters
    request_body:
      task: string
      context: object
      parameters: object
    responses:
      200: Task execution result
      400: Invalid request
      500: Execution error
  
  GET /api/v1/agents/{agent-id}/status:
    description: Get agent status and health information
    responses:
      200: Agent status and metrics
      503: Agent unavailable
  
  POST /api/v1/agents/{agent-id}/feedback:
    description: Provide feedback on agent performance
    request_body:
      execution_id: string
      rating: integer
      feedback: string
```

### Event-Driven Integration
- **Event Publishing:** {Events this agent publishes to the system}
- **Event Consumption:** {Events this agent subscribes to and processes}
- **Event Schema:** {Standardized event format and validation}
- **Error Handling:** {Event processing error handling and retry logic}

---

## 6. Data Access and Management

### Data Sources
```yaml
Data Access Patterns:
  Read-Only Access:
    - {Data Source}: {Purpose and access pattern}
    - {Data Source}: {Purpose and access pattern}
  
  Read-Write Access:
    - {Data Source}: {Purpose and modification patterns}
    - {Data Source}: {Purpose and modification patterns}
  
  Real-time Streams:
    - {Stream Source}: {Processing requirements and latency}
    - {Stream Source}: {Processing requirements and latency}
```

### Data Privacy and Security
- **Data Classification:** {Sensitivity levels and handling requirements}
- **Access Controls:** {Authentication and authorization requirements}
- **Data Encryption:** {Encryption requirements for data at rest and in transit}
- **Audit Logging:** {Data access logging and compliance requirements}

### Data Processing
- **Input Validation:** {Data validation and sanitization procedures}
- **Data Transformation:** {Data processing and normalization requirements}
- **Output Formatting:** {Response formatting and data presentation}
- **Caching Strategy:** {Data caching and performance optimization}

---

## 7. AI Governance and Ethics

### Ethical Guidelines
- **Fairness and Bias:** {Bias detection and mitigation strategies}
- **Transparency:** {Explainability and decision transparency requirements}
- **Accountability:** {Responsibility and audit trail requirements}
- **Privacy Protection:** {User privacy and data protection measures}

### Safety and Guardrails
```yaml
Safety Mechanisms:
  Input Validation:
    - Content Filtering: {Inappropriate content detection and blocking}
    - Injection Prevention: {Prompt injection and manipulation protection}
    - Rate Limiting: {Request rate limiting and abuse prevention}
  
  Output Validation:
    - Content Review: {Output content validation and filtering}
    - Fact Checking: {Accuracy verification and source validation}
    - Harm Prevention: {Harmful content detection and blocking}
  
  Behavioral Constraints:
    - Scope Limitations: {Agent capability boundaries and restrictions}
    - Action Approval: {Required approvals for sensitive actions}
    - Escalation Triggers: {Automatic escalation conditions}
```

### Compliance Framework
- **Regulatory Requirements:** {Applicable regulations and compliance needs}
- **Audit Requirements:** {Audit trail and documentation requirements}
- **Risk Assessment:** {Risk identification and mitigation strategies}
- **Governance Oversight:** {Governance committee and review processes}

---

## 8. Performance and Monitoring

### Performance Metrics
```yaml
Key Performance Indicators:
  Response Metrics:
    - Response Time: {Target latency and performance thresholds}
    - Throughput: {Requests per second and capacity limits}
    - Availability: {Uptime requirements and SLA targets}
  
  Quality Metrics:
    - Accuracy: {Task completion accuracy and error rates}
    - User Satisfaction: {User feedback and satisfaction scores}
    - Task Success Rate: {Successful task completion percentage}
  
  Business Metrics:
    - Cost Efficiency: {Cost per task and resource utilization}
    - Business Impact: {Business value and outcome metrics}
    - Adoption Rate: {User adoption and engagement metrics}
```

### Monitoring and Alerting
- **Health Monitoring:** {Agent health checks and status monitoring}
- **Performance Monitoring:** {Response time, throughput, and resource usage}
- **Error Monitoring:** {Error detection, classification, and alerting}
- **Business Monitoring:** {Business metric tracking and trend analysis}

### Observability Integration
- **Distributed Tracing:** {Request tracing across agent and service boundaries}
- **Structured Logging:** {Comprehensive logging for debugging and analysis}
- **Metrics Collection:** {Custom metrics and performance data collection}
- **Dashboard Design:** {Monitoring dashboards and visualization}

---

## 9. Deployment and Operations

### Deployment Strategy
- **Containerization:** {Docker configuration and container requirements}
- **Orchestration:** {Kubernetes deployment and scaling configuration}
- **Environment Management:** {Development, staging, and production environments}
- **Configuration Management:** {Environment-specific configuration and secrets}

### Scaling and Resource Management
- **Horizontal Scaling:** {Auto-scaling policies and resource allocation}
- **Vertical Scaling:** {Resource requirements and capacity planning}
- **Load Balancing:** {Traffic distribution and load balancing strategies}
- **Resource Optimization:** {Cost optimization and resource efficiency}

### Operational Procedures
- **Deployment Process:** {CI/CD pipeline and deployment automation}
- **Rollback Procedures:** {Rollback strategies and emergency procedures}
- **Maintenance Windows:** {Scheduled maintenance and update procedures}
- **Incident Response:** {Incident detection, response, and resolution procedures}

---

## 10. Testing and Validation

### Testing Strategy
```yaml
Testing Approaches:
  Unit Testing:
    - Component Testing: {Individual agent component validation}
    - Function Testing: {Agent capability and function testing}
    - Mock Integration: {Mocked service and data integration testing}
  
  Integration Testing:
    - Service Integration: {Real service integration validation}
    - Multi-Agent Testing: {Agent coordination and collaboration testing}
    - End-to-End Testing: {Complete workflow and user journey testing}
  
  Performance Testing:
    - Load Testing: {Performance under expected load}
    - Stress Testing: {Performance under extreme conditions}
    - Scalability Testing: {Scaling behavior and resource usage}
```

### Validation Criteria
- **Functional Validation:** {Agent capability and feature validation}
- **Performance Validation:** {Response time and throughput validation}
- **Security Validation:** {Security control and vulnerability testing}
- **Compliance Validation:** {Regulatory and governance requirement validation}

---

## 11. Documentation and Training

### Documentation Requirements
- **User Documentation:** {User guides and interaction documentation}
- **Developer Documentation:** {API documentation and integration guides}
- **Operational Documentation:** {Deployment and operational procedures}
- **Troubleshooting Guides:** {Common issues and resolution procedures}

### Training and Onboarding
- **User Training:** {End-user training and adoption programs}
- **Developer Training:** {Integration and development training}
- **Operational Training:** {Operations and maintenance training}
- **Continuous Learning:** {Ongoing training and skill development}

---

## 12. Success Criteria and Acceptance

### Functional Acceptance Criteria
- [ ] Agent successfully performs all specified capabilities
- [ ] Human-AI collaboration workflows function as designed
- [ ] Multi-agent coordination operates correctly
- [ ] Service integration works reliably

### Performance Acceptance Criteria
- [ ] Response time meets specified SLA requirements
- [ ] Throughput handles expected load capacity
- [ ] Availability meets uptime requirements
- [ ] Resource usage stays within allocated limits

### Quality Acceptance Criteria
- [ ] Task accuracy meets specified thresholds
- [ ] User satisfaction scores meet targets
- [ ] Error rates stay below acceptable limits
- [ ] Security and compliance requirements are met

---

## 13. Change Log

| Version | Date | Changes | Author | Approver |
|---------|------|---------|--------|----------|
| 1.0 | {Date} | Initial agent specification | {Author} | {Approver} |

---

## 14. References and Dependencies

- **Related Agents:** {Links to related agent specifications}
- **Service Dependencies:** {Links to dependent service documentation}
- **Architecture Documents:** {Links to system architecture documentation}
- **Governance Policies:** {Links to AI governance and ethics policies}

==================== END: ai-agent-integration-tmpl ====================


==================== START: api-gateway-configuration-tmpl ====================
# API Gateway Configuration Template

## Document Information
- **Document Type**: API Gateway Configuration
- **Version**: 1.0
- **Status**: [Draft/Review/Approved]
- **Author**: [Service Mesh Architect Name]
- **Date**: [Creation Date]
- **Last Updated**: [Last Update Date]

## Executive Summary

### Gateway Overview
- **Project Name**: [Project Name]
- **Gateway Type**: [Kong/NGINX/AWS API Gateway/Istio Gateway]
- **Deployment Model**: [Centralized/Distributed/Hybrid]
- **Microfrontend Count**: [Number of microfrontends]
- **Backend Services**: [Number of backend services]

### Key Objectives
- **Unified Entry Point**: Single point of access for all frontend services
- **Security Enforcement**: Authentication, authorization, and rate limiting
- **Traffic Management**: Load balancing, routing, and failover
- **Observability**: Logging, monitoring, and tracing
- **Protocol Translation**: HTTP/HTTPS, WebSocket, gRPC support

## Gateway Architecture

### High-Level Architecture
```
Internet/CDN
    ↓
Load Balancer (L4/L7)
    ↓
API Gateway Cluster
├── Authentication Service
├── Authorization Service
├── Rate Limiting Service
├── Caching Layer
└── Monitoring/Logging
    ↓
Service Mesh (Istio/Linkerd)
    ↓
Backend Services
├── User Service
├── Product Service
├── Order Service
└── Notification Service
```

### Gateway Deployment
```yaml
# Kubernetes deployment for API Gateway
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  labels:
    app: api-gateway
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
        version: v1
    spec:
      containers:
      - name: gateway
        image: kong:3.4
        ports:
        - containerPort: 8000
          name: proxy
        - containerPort: 8001
          name: admin
        env:
        - name: KONG_DATABASE
          value: "postgres"
        - name: KONG_PG_HOST
          value: "postgres-service"
        - name: KONG_PROXY_ACCESS_LOG
          value: "/dev/stdout"
        - name: KONG_ADMIN_ACCESS_LOG
          value: "/dev/stdout"
        - name: KONG_PROXY_ERROR_LOG
          value: "/dev/stderr"
        - name: KONG_ADMIN_ERROR_LOG
          value: "/dev/stderr"
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /status
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /status
            port: 8001
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Routing Configuration

### Frontend Service Routing
```yaml
# Kong service and route configuration
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: shell-application
spec:
  host: shell-app-service
  port: 80
  protocol: http
  path: /
---
apiVersion: configuration.konghq.com/v1
kind: KongRoute
metadata:
  name: shell-app-route
spec:
  service: shell-application
  protocols:
  - http
  - https
  hosts:
  - app.company.com
  paths:
  - /
  strip_path: false
---
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: user-microfrontend
spec:
  host: user-mf-service
  port: 80
  protocol: http
  path: /
---
apiVersion: configuration.konghq.com/v1
kind: KongRoute
metadata:
  name: user-mf-route
spec:
  service: user-microfrontend
  protocols:
  - http
  - https
  hosts:
  - app.company.com
  paths:
  - /user
  - /profile
  strip_path: false
```

### Backend API Routing
```yaml
# Backend service routing
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: user-api
spec:
  host: user-api-service
  port: 8080
  protocol: http
  path: /api/v1
---
apiVersion: configuration.konghq.com/v1
kind: KongRoute
metadata:
  name: user-api-route
spec:
  service: user-api
  protocols:
  - http
  - https
  hosts:
  - api.company.com
  paths:
  - /api/users
  strip_path: true
  plugins:
  - name: rate-limiting
    config:
      minute: 100
      hour: 1000
  - name: cors
    config:
      origins:
      - "https://app.company.com"
      methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
      headers:
      - Accept
      - Authorization
      - Content-Type
      - X-Requested-With
      credentials: true
```

## Security Configuration

### Authentication Setup
```yaml
# OAuth 2.0 / OIDC Plugin Configuration
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: oidc-auth
plugin: openid-connect
config:
  issuer: "https://auth.company.com/realms/company"
  client_id: "api-gateway"
  client_secret: "gateway-secret"
  redirect_uri: "https://app.company.com/auth/callback"
  scope: "openid profile email"
  response_type: "code"
  ssl_verify: true
  session_secret: "session-secret-key"
  recovery_page_path: "/auth/error"
---
# Apply to routes requiring authentication
apiVersion: configuration.konghq.com/v1
kind: KongRoute
metadata:
  name: protected-route
  annotations:
    konghq.com/plugins: oidc-auth
spec:
  service: protected-service
  protocols:
  - https
  hosts:
  - api.company.com
  paths:
  - /api/protected
```

### JWT Validation
```yaml
# JWT Plugin Configuration
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: jwt-auth
plugin: jwt
config:
  uri_param_names:
  - jwt
  header_names:
  - Authorization
  claims_to_verify:
  - exp
  - iat
  - iss
  - aud
  key_claim_name: iss
  secret_is_base64: false
  run_on_preflight: true
---
# JWT Consumer
apiVersion: configuration.konghq.com/v1
kind: KongConsumer
metadata:
  name: frontend-app
username: frontend-app
---
apiVersion: configuration.konghq.com/v1
kind: KongCredential-jwt
metadata:
  name: frontend-jwt
consumerRef: frontend-app
type: jwt
config:
  key: "frontend-app"
  secret: "jwt-secret-key"
  algorithm: "HS256"
```

### Rate Limiting
```yaml
# Rate Limiting Configuration
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: api-rate-limit
plugin: rate-limiting
config:
  minute: 100
  hour: 1000
  day: 10000
  policy: "redis"
  redis_host: "redis-service"
  redis_port: 6379
  redis_database: 0
  hide_client_headers: false
  fault_tolerant: true
---
# Advanced Rate Limiting by Consumer
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: consumer-rate-limit
plugin: rate-limiting-advanced
config:
  limit:
  - 100
  window_size:
  - 60
  identifier: consumer
  sync_rate: 10
  strategy: redis
  redis:
    host: redis-service
    port: 6379
    database: 0
```

## Load Balancing and Health Checks

### Upstream Configuration
```yaml
# Upstream service configuration
apiVersion: configuration.konghq.com/v1
kind: KongUpstream
metadata:
  name: user-service-upstream
spec:
  name: user-service
  algorithm: round-robin
  hash_on: none
  hash_fallback: none
  healthchecks:
    active:
      type: http
      http_path: /health
      healthy:
        interval: 10
        successes: 3
      unhealthy:
        interval: 10
        http_failures: 3
        timeouts: 3
    passive:
      type: http
      healthy:
        successes: 3
      unhealthy:
        http_failures: 3
        timeouts: 3
---
# Target endpoints
apiVersion: configuration.konghq.com/v1
kind: KongTarget
metadata:
  name: user-service-target-1
spec:
  upstream: user-service
  target: user-service-1:8080
  weight: 100
---
apiVersion: configuration.konghq.com/v1
kind: KongTarget
metadata:
  name: user-service-target-2
spec:
  upstream: user-service
  target: user-service-2:8080
  weight: 100
```

### Circuit Breaker
```yaml
# Circuit Breaker Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: circuit-breaker
plugin: proxy-cache
config:
  response_code:
  - 200
  - 301
  - 404
  request_method:
  - GET
  - HEAD
  content_type:
  - text/plain
  - application/json
  cache_ttl: 300
  cache_control: false
  storage_ttl: 3600
---
# Timeout configuration
apiVersion: configuration.konghq.com/v1
kind: KongService
metadata:
  name: timeout-service
spec:
  host: backend-service
  port: 8080
  protocol: http
  connect_timeout: 5000
  write_timeout: 10000
  read_timeout: 10000
  retries: 3
```

## Caching Strategy

### Response Caching
```yaml
# Proxy Cache Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: response-cache
plugin: proxy-cache
config:
  response_code:
  - 200
  - 301
  - 404
  request_method:
  - GET
  - HEAD
  content_type:
  - text/plain
  - application/json
  - text/html
  cache_ttl: 300
  cache_control: true
  storage_ttl: 3600
  strategy: memory
  memory:
    dictionary_name: kong_db_cache
---
# Redis-based caching
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: redis-cache
plugin: proxy-cache
config:
  strategy: redis
  redis:
    host: redis-service
    port: 6379
    database: 1
    timeout: 2000
  cache_ttl: 600
  storage_ttl: 7200
```

### CDN Integration
```yaml
# CDN Cache Control Headers
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: cdn-headers
plugin: response-transformer
config:
  add:
    headers:
    - "Cache-Control: public, max-age=3600"
    - "CDN-Cache-Control: max-age=86400"
    - "Vary: Accept-Encoding"
  remove:
    headers:
    - "Server"
    - "X-Powered-By"
```

## Monitoring and Observability

### Logging Configuration
```yaml
# HTTP Log Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: http-log
plugin: http-log
config:
  http_endpoint: "https://logs.company.com/api/logs"
  method: POST
  timeout: 10000
  keepalive: 60000
  content_type: application/json
  flush_timeout: 2
  retry_count: 10
  queue_size: 1
---
# File Log Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: file-log
plugin: file-log
config:
  path: /var/log/kong/access.log
  reopen: true
```

### Metrics Collection
```yaml
# Prometheus Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: prometheus
plugin: prometheus
config:
  per_consumer: true
  status_code_metrics: true
  latency_metrics: true
  bandwidth_metrics: true
  upstream_health_metrics: true
---
# StatsD Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: statsd
plugin: statsd
config:
  host: statsd-service
  port: 8125
  metrics:
  - request_count
  - request_size
  - response_size
  - latency
  - status_count
  - unique_users
  - request_per_user
  - upstream_latency
  - kong_latency
```

### Distributed Tracing
```yaml
# Zipkin Tracing Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: zipkin
plugin: zipkin
config:
  http_endpoint: "http://zipkin-service:9411/api/v2/spans"
  sample_ratio: 0.1
  include_credential: true
  traceid_byte_count: 16
  header_type: preserve
  default_header_type: b3
  tags:
    service: api-gateway
    environment: production
```

## Error Handling and Resilience

### Error Response Templates
```yaml
# Custom Error Pages
apiVersion: v1
kind: ConfigMap
metadata:
  name: error-pages
data:
  404.html: |
    <!DOCTYPE html>
    <html>
    <head>
        <title>Page Not Found</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error-code { font-size: 72px; color: #e74c3c; }
            .error-message { font-size: 24px; color: #34495e; }
        </style>
    </head>
    <body>
        <div class="error-code">404</div>
        <div class="error-message">The page you're looking for doesn't exist.</div>
        <a href="/">Return to Home</a>
    </body>
    </html>
  
  500.html: |
    <!DOCTYPE html>
    <html>
    <head>
        <title>Server Error</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error-code { font-size: 72px; color: #e74c3c; }
            .error-message { font-size: 24px; color: #34495e; }
        </style>
    </head>
    <body>
        <div class="error-code">500</div>
        <div class="error-message">Something went wrong on our end.</div>
        <a href="/">Return to Home</a>
    </body>
    </html>
```

### Retry and Timeout Configuration
```yaml
# Request Termination Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: request-termination
plugin: request-termination
config:
  status_code: 503
  content_type: application/json
  body: '{"error": "Service temporarily unavailable"}'
  trigger: "X-Terminate"
---
# Request Size Limiting
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: request-size-limit
plugin: request-size-limiting
config:
  allowed_payload_size: 10
  size_unit: megabytes
  require_content_length: false
```

## Security Headers and CORS

### Security Headers
```yaml
# Security Headers Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: security-headers
plugin: response-transformer
config:
  add:
    headers:
    - "X-Content-Type-Options: nosniff"
    - "X-Frame-Options: DENY"
    - "X-XSS-Protection: 1; mode=block"
    - "Strict-Transport-Security: max-age=31536000; includeSubDomains"
    - "Referrer-Policy: strict-origin-when-cross-origin"
    - "Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
  remove:
    headers:
    - "Server"
    - "X-Powered-By"
```

### CORS Configuration
```yaml
# CORS Plugin
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: cors-policy
plugin: cors
config:
  origins:
  - "https://app.company.com"
  - "https://admin.company.com"
  methods:
  - GET
  - POST
  - PUT
  - DELETE
  - OPTIONS
  - HEAD
  headers:
  - Accept
  - Accept-Version
  - Authorization
  - Content-Length
  - Content-MD5
  - Content-Type
  - Date
  - X-Auth-Token
  - X-Requested-With
  exposed_headers:
  - X-Auth-Token
  - X-RateLimit-Limit
  - X-RateLimit-Remaining
  - X-RateLimit-Reset
  credentials: true
  max_age: 3600
  preflight_continue: false
```

## Environment Configuration

### Development Environment
```yaml
# Development Gateway Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: gateway-dev-config
data:
  kong.conf: |
    database = postgres
    pg_host = postgres-dev
    pg_port = 5432
    pg_database = kong_dev
    pg_user = kong
    pg_password = kong_password
    
    proxy_listen = 0.0.0.0:8000
    admin_listen = 0.0.0.0:8001
    
    log_level = debug
    proxy_access_log = /dev/stdout
    proxy_error_log = /dev/stderr
    admin_access_log = /dev/stdout
    admin_error_log = /dev/stderr
    
    plugins = bundled,oidc,prometheus
```

### Production Environment
```yaml
# Production Gateway Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: gateway-prod-config
data:
  kong.conf: |
    database = postgres
    pg_host = postgres-prod
    pg_port = 5432
    pg_database = kong_prod
    pg_user = kong
    pg_password_file = /etc/secrets/pg_password
    
    proxy_listen = 0.0.0.0:8000 ssl
    admin_listen = 127.0.0.1:8001
    
    log_level = notice
    proxy_access_log = /var/log/kong/access.log
    proxy_error_log = /var/log/kong/error.log
    
    ssl_cert = /etc/ssl/certs/gateway.crt
    ssl_cert_key = /etc/ssl/private/gateway.key
    
    plugins = bundled,oidc,prometheus
    
    nginx_worker_processes = auto
    nginx_daemon = off
```

## Disaster Recovery and Backup

### Configuration Backup
```bash
#!/bin/bash
# Gateway configuration backup script

BACKUP_DIR="/backups/gateway/$(date +%Y%m%d_%H%M%S)"
KONG_ADMIN_URL="http://kong-admin:8001"

mkdir -p "$BACKUP_DIR"

# Export Kong configuration
curl -s "$KONG_ADMIN_URL/config" > "$BACKUP_DIR/kong-config.json"

# Export services
curl -s "$KONG_ADMIN_URL/services" > "$BACKUP_DIR/services.json"

# Export routes
curl -s "$KONG_ADMIN_URL/routes" > "$BACKUP_DIR/routes.json"

# Export plugins
curl -s "$KONG_ADMIN_URL/plugins" > "$BACKUP_DIR/plugins.json"

# Export consumers
curl -s "$KONG_ADMIN_URL/consumers" > "$BACKUP_DIR/consumers.json"

# Create archive
tar -czf "$BACKUP_DIR.tar.gz" -C "$BACKUP_DIR" .
rm -rf "$BACKUP_DIR"

echo "Backup completed: $BACKUP_DIR.tar.gz"
```

### Disaster Recovery Plan
1. **Assess Impact**: Determine scope of gateway failure
2. **Activate Backup Gateway**: Switch traffic to backup instance
3. **Restore Configuration**: Apply latest configuration backup
4. **Validate Services**: Verify all routes and plugins are working
5. **Monitor Performance**: Ensure normal operation is restored
6. **Post-Incident Review**: Analyze failure and improve procedures

---

*This template provides a comprehensive framework for API Gateway configuration in microfrontend architectures. Customize based on your specific gateway technology and requirements.*

==================== END: api-gateway-configuration-tmpl ====================


==================== START: architecture-tmpl ====================
# {Project Name} Architecture Document

## Introduction / Preamble

{This document outlines the overall project architecture, including backend systems, shared services, and non-UI specific concerns. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development, ensuring consistency and adherence to chosen patterns and technologies.

**Relationship to Frontend Architecture:**
If the project includes a significant user interface, a separate Frontend Architecture Document (typically named `front-end-architecture-tmpl.txt` or similar, and linked in the "Key Reference Documents" section) details the frontend-specific design and MUST be used in conjunction with this document. Core technology stack choices documented herein (see "Definitive Tech Stack Selections") are definitive for the entire project, including any frontend components.}

## Table of Contents

{ Update this if sections and subsections are added or removed }

## Technical Summary

{ Provide a brief paragraph overview of the system's architecture, key components, technology choices, and architectural patterns used. Reference the goals from the PRD. }

## High-Level Overview

{ Describe the main architectural style (e.g., Monolith, Microservices, Serverless, Event-Driven), reflecting the decision made in the PRD. Explain the repository structure (Monorepo/Polyrepo). Explain the primary user interaction or data flow at a conceptual level. }

{ Insert high-level mermaid system context or interaction diagram here - e.g., Mermaid Class C4 Models Layer 1 and 2 }

## Architectural / Design Patterns Adopted

{ List the key high-level patterns chosen for the architecture. These foundational patterns should be established early as they guide component design, interactions, and technology choices. }

- **Pattern 1:** {e.g., Serverless, Event-Driven, Microservices, CQRS} - _Rationale/Reference:_ {Briefly why, or link to a more detailed explanation if needed}
- **Pattern 2:** {e.g., Dependency Injection, Repository Pattern, Module Pattern} - _Rationale/Reference:_ {...}
- **Pattern N:** {...}

## Component View

{ Describe the major logical components or services of the system and their responsibilities, reflecting the decided overall architecture (e.g., distinct microservices, modules within a monolith, packages within a monorepo) and the architectural patterns adopted. Explain how they collaborate. }

- Component A: {Description of responsibility}

{Insert component diagram here if it helps - e.g., using Mermaid graph TD or C4 Model Container/Component Diagram}

- Component N...: {Description of responsibility}

{ Insert component diagram here if it helps - e.g., using Mermaid graph TD or C4 Model Container/Component Diagram }

## Project Structure

{Provide an ASCII or Mermaid diagram representing the project's folder structure. The following is a general example. If a `front-end-architecture-tmpl.txt` (or equivalent) is in use, it will contain the detailed structure for the frontend portion (e.g., within `src/frontend/` or a dedicated `frontend/` root directory). Shared code structure (e.g., in a `packages/` directory for a monorepo) should also be detailed here.}

```plaintext
{project-root}/
├── .github/                    # CI/CD workflows (e.g., GitHub Actions)
│   └── workflows/
│       └── main.yml
├── .vscode/                    # VSCode settings (optional)
│   └── settings.json
├── build/                      # Compiled output (if applicable, often git-ignored)
├── config/                     # Static configuration files (if any)
├── docs/                       # Project documentation (PRD, Arch, etc.)
│   ├── index.md
│   └── ... (other .md files)
├── infra/                      # Infrastructure as Code (e.g., CDK, Terraform)
│   └── lib/
│   └── bin/
├── node_modules/ / venv / target/ # Project dependencies (git-ignored)
├── scripts/                    # Utility scripts (build, deploy helpers, etc.)
├── src/                        # Application source code
│   ├── backend/                # Backend-specific application code (if distinct frontend exists)
│   │   ├── core/               # Core business logic, domain models
│   │   ├── services/           # Business services, orchestrators
│   │   ├── adapters/           # Adapters to external systems (DB, APIs)
│   │   ├── controllers/ / routes/ # API endpoint handlers
│   │   └── main.ts / app.py    # Backend application entry point
│   ├── frontend/               # Placeholder: See Frontend Architecture Doc for details if used
│   ├── shared/ / common/       # Code shared (e.g., types, utils, domain models if applicable)
│   │   └── types/
│   └── main.ts / index.ts / app.ts # Main application entry point (if not using backend/frontend split above)
├── stories/                    # Generated story files for development (optional)
│   └── epic1/
├── test/                       # Automated tests
│   ├── unit/                   # Unit tests (mirroring src structure)
│   ├── integration/            # Integration tests
│   └── e2e/                    # End-to-end tests
├── .env.example                # Example environment variables
├── .gitignore                  # Git ignore rules
├── package.json / requirements.txt / pom.xml # Project manifest and dependencies
├── tsconfig.json / pyproject.toml # Language-specific configuration (if applicable)
├── Dockerfile                  # Docker build instructions (if applicable)
└── README.md                   # Project overview and setup instructions
```

(Adjust the example tree based on the actual project type - e.g., Python would have requirements.txt, etc. The structure above illustrates a potential separation for projects with distinct frontends; for simpler projects or APIs, the `src/` structure might be flatter.)

### Key Directory Descriptions

- docs/: Contains all project planning and reference documentation.
- infra/: Holds the Infrastructure as Code definitions (e.g., AWS CDK, Terraform).
- src/: Contains the main application source code. May be subdivided (e.g., `backend/`, `frontend/`, `shared/`) depending on project complexity and whether a separate frontend architecture document is in use.
- src/backend/core/ / src/core/ / src/domain/: Core business logic, entities, use cases, independent of frameworks/external services.
- src/backend/adapters/ / src/adapters/ / src/infrastructure/: Implementation details, interactions with databases, cloud SDKs, frameworks.
- src/backend/controllers/ / src/routes/ / src/pages/: Entry points for API requests or UI views (if UI is simple and not in a separate frontend structure).
- test/: Contains all automated tests, mirroring the src/ structure where applicable.

### Notes

{Mention any specific build output paths, compiler configuration pointers, or other relevant structural notes.}

## API Reference

### External APIs Consumed

{Repeat this section for each external API the system interacts with.}

#### {External Service Name} API

- **Purpose:** {Why does the system use this API?}
- **Base URL(s):**
  - Production: `{URL}`
  - Staging/Dev: `{URL}`
- **Authentication:** {Describe method - e.g., API Key in Header (Header Name: `X-API-Key`), OAuth 2.0 Client Credentials, Basic Auth. Reference `docs/environment-vars.md` for key names.}
- **Key Endpoints Used:**
  - **`{HTTP Method} {/path/to/endpoint}`:**
    - Description: {What does this endpoint do?}
    - Request Parameters: {Query params, path params}
    - Request Body Schema: {Provide JSON schema inline, or link to a detailed definition in `docs/data-models.md` only if the schema is exceptionally large or complex.}
    - Example Request: `{Code block}`
    - Success Response Schema (Code: `200 OK`): {Provide JSON schema inline, or link to a detailed definition in `docs/data-models.md` only if very complex.}
    - Error Response Schema(s) (Codes: `4xx`, `5xx`): {Provide JSON schema inline, or link to a detailed definition in `docs/data-models.md` only if very complex.}
    - Example Response: `{Code block}`
  - **`{HTTP Method} {/another/endpoint}`:** {...}
- **Rate Limits:** {If known}
- **Link to Official Docs:** {URL}

### Internal APIs Provided (If Applicable)

{If the system exposes its own APIs (e.g., in a microservices architecture or for a UI frontend). Repeat for each API.}

#### {Internal API / Service Name} API

- **Purpose:** {What service does this API provide?}
- **Base URL(s):** {e.g., `/api/v1/...`}
- **Authentication/Authorization:** {Describe how access is controlled.}
- **Endpoints:**
  - **`{HTTP Method} {/path/to/endpoint}`:**
    - Description: {What does this endpoint do?}
    - Request Parameters: {...}
    - Request Body Schema: {Provide JSON schema inline, or link to a detailed definition in `docs/data-models.md` only if very complex.}
    - Success Response Schema (Code: `200 OK`): {Provide JSON schema inline, or link to a detailed definition in `docs/data-models.md` only if very complex.}
    - Error Response Schema(s) (Codes: `4xx`, `5xx`): {Provide JSON schema inline, or link to a detailed definition in `docs/data-models.md` only if very complex.}
  - **`{HTTP Method} {/another/endpoint}`:** {...}

## Data Models

### Core Application Entities / Domain Objects

{Define the main objects/concepts the application works with. Repeat subsection for each key entity.}

#### {Entity Name, e.g., User, Order, Product}

- **Description:** {What does this entity represent?}
- **Schema / Interface Definition:**
  ```typescript
  // Example using TypeScript Interface
  export interface {EntityName} {
    id: string; // {Description, e.g., Unique identifier}
    propertyName: string; // {Description}
    optionalProperty?: number; // {Description}
    // ... other properties
  }
  ```
- **Validation Rules:** {List any specific validation rules beyond basic types - e.g., max length, format, range.}

### API Payload Schemas (If distinct)

{Define schemas here only if they are distinct from core entities AND not fully detailed under the API endpoint definitions in the API Reference section. Prefer detailing request/response schemas directly with their APIs where possible. This section is for complex, reusable payload structures that might be used across multiple internal APIs or differ significantly from core persisted entities.}

#### {API Endpoint / Purpose, e.g., Create Order Request, repeat the section as needed}

- **Schema / Interface Definition:**
  ```typescript
  // Example
  export interface CreateOrderRequest {
    customerId: string;
    items: { productId: string; quantity: number }[];
    // ...
  }
  ```

### Database Schemas (If applicable)

{If using a database, define table structures or document database schemas. repeat as needed}

#### {Table / Collection Name}

- **Purpose:** {What data does this table store?}
- **Schema Definition:**
  ```sql
  -- Example SQL
  CREATE TABLE {TableName} (
    id VARCHAR(36) PRIMARY KEY,
    column_name VARCHAR(255) NOT NULL,
    numeric_column DECIMAL(10, 2),
    -- ... other columns, indexes, constraints
  );
  ```
  _(Alternatively, use ORM model definitions, NoSQL document structure, etc.)_

## Core Workflow / Sequence Diagrams

{ Illustrate key or complex workflows using mermaid sequence diagrams. Can have high level tying the full project together, and also smaller epic level sequence diagrams. }

## Definitive Tech Stack Selections

{ This section outlines the definitive technology choices for the project. These selections should be made after a thorough understanding of the project's requirements, components, data models, and core workflows. The Architect Agent should guide the user through these decisions, ensuring each choice is justified and recorded accurately in the table below.

This table is the **single source of truth** for all technology selections. Other architecture documents (e.g., Frontend Architecture) must refer to these choices and elaborate on their specific application rather than re-defining them.

Key decisions to discuss and finalize here, which will then be expanded upon and formally documented in the detailed stack table below, include considerations such as:

- Preferred Starter Template Frontend: { Url to template or starter, if used }
- Preferred Starter Template Backend: { Url to template or starter, if used }
- Primary Language(s) & Version(s): {e.g., TypeScript 5.x, Python 3.11 - Specify exact versions, e.g., `5.2.3`}
- Primary Runtime(s) & Version(s): {e.g., Node.js 22.x - Specify exact versions, e.g., `22.0.1`}

Must be definitive selections; do not list open-ended choices (e.g., for web scraping, pick one tool, not two). Specify exact versions (e.g., `18.2.0`). If 'Latest' is used, it implies the latest stable version _at the time of this document's last update_, and the specific version (e.g., `xyz-library@2.3.4`) should be recorded. Pinning versions is strongly preferred to avoid unexpected breaking changes for the AI agent. }

| Category             | Technology              | Version / Details | Description / Purpose                   | Justification (Optional) |
| :------------------- | :---------------------- | :---------------- | :-------------------------------------- | :----------------------- |
| **Languages**        | {e.g., TypeScript}      | {e.g., 5.x}       | {Primary language for backend/frontend} | {Why this language?}     |
|                      | {e.g., Python}          | {e.g., 3.11}      | {Used for data processing, ML}          | {...}                    |
| **Runtime**          | {e.g., Node.js}         | {e.g., 22.x}      | {Server-side execution environment}     | {...}                    |
| **Frameworks**       | {e.g., NestJS}          | {e.g., 10.x}      | {Backend API framework}                 | {Why this framework?}    |
|                      | {e.g., React}           | {e.g., 18.x}      | {Frontend UI library}                   | {...}                    |
| **Databases**        | {e.g., PostgreSQL}      | {e.g., 15}        | {Primary relational data store}         | {...}                    |
|                      | {e.g., Redis}           | {e.g., 7.x}       | {Caching, session storage}              | {...}                    |
| **Cloud Platform**   | {e.g., AWS}             | {N/A}             | {Primary cloud provider}                | {...}                    |
| **Cloud Services**   | {e.g., AWS Lambda}      | {N/A}             | {Serverless compute}                    | {...}                    |
|                      | {e.g., AWS S3}          | {N/A}             | {Object storage for assets/state}       | {...}                    |
|                      | {e.g., AWS EventBridge} | {N/A}             | {Event bus / scheduled tasks}           | {...}                    |
| **Infrastructure**   | {e.g., AWS CDK}         | {e.g., Latest}    | {Infrastructure as Code tool}           | {...}                    |
|                      | {e.g., Docker}          | {e.g., Latest}    | {Containerization}                      | {...}                    |
| **UI Libraries**     | {e.g., Material UI}     | {e.g., 5.x}       | {React component library}               | {...}                    |
| **State Management** | {e.g., Redux Toolkit}   | {e.g., Latest}    | {Frontend state management}             | {...}                    |
| **Testing**          | {e.g., Jest}            | {e.g., Latest}    | {Unit/Integration testing framework}    | {...}                    |
|                      | {e.g., Playwright}      | {e.g., Latest}    | {End-to-end testing framework}          | {...}                    |
| **CI/CD**            | {e.g., GitHub Actions}  | {N/A}             | {Continuous Integration/Deployment}     | {...}                    |
| **Other Tools**      | {e.g., LangChain.js}    | {e.g., Latest}    | {LLM interaction library}               | {...}                    |
|                      | {e.g., Cheerio}         | {e.g., Latest}    | {HTML parsing/scraping}                 | {...}                    |

## Infrastructure and Deployment Overview

- Cloud Provider(s): {e.g., AWS, Azure, GCP, On-premise}
- Core Services Used: {List key managed services - e.g., Lambda, S3, Kubernetes Engine, RDS, Kafka}
- Infrastructure as Code (IaC): {Tool used - e.g., AWS CDK, Terraform...} - Location: {Link to IaC code repo/directory}
- Deployment Strategy: {e.g., CI/CD pipeline with automated promotions, Blue/Green, Canary} - Tools: {e.g., Jenkins, GitHub Actions, GitLab CI}
- Environments: {List environments - e.g., Development, Staging, Production}
- Environment Promotion: {Describe steps, e.g., `dev` -\> `staging` (manual approval / automated tests pass) -\> `production` (automated after tests pass and optional manual approval)}
- Rollback Strategy: {e.g., Automated rollback on health check failure post-deployment, Manual trigger via CI/CD job, IaC state rollback. Specify primary mechanism.}

## Error Handling Strategy

- **General Approach:** {e.g., Use exceptions as primary mechanism, return error codes/tuples for specific modules, clearly defined custom error types hierarchy.}
- **Logging:**
  - Library/Method: {e.g., `console.log/error` (Node.js), Python `logging` module with `structlog`, dedicated logging library like `Pino` or `Serilog`. Specify the chosen library.}
  - Format: {e.g., JSON, plain text with timestamp and severity. JSON is preferred for structured logging.}
  - Levels: {e.g., DEBUG, INFO, WARN, ERROR, CRITICAL. Specify standard usage for each.}
  - Context: {What contextual information must be included? e.g., Correlation ID, User ID (if applicable and safe), Service Name, Operation Name, Key Parameters (sanitized).}
- **Specific Handling Patterns:**
  - External API Calls: {Define retry mechanisms (e.g., exponential backoff, max retries - specify library if one is mandated like `Polly` or `tenacity`), circuit breaker pattern usage (e.g., using `resilience4j` or equivalent - specify if and how), timeout configurations (connect and read timeouts). How are API errors (4xx, 5xx) translated or propagated?}
  - Internal Errors / Business Logic Exceptions: {How to convert internal errors to user-facing errors if applicable (e.g., generic error messages with a unique ID for support, specific error codes). Are there defined business exception classes?}
  - Transaction Management: {Approach to ensure data consistency in case of errors during multi-step operations, e.g., database transactions (specify isolation levels if non-default), Saga pattern for distributed transactions (specify orchestrator/choreography and compensation logic).}

## Coding Standards

{These standards are mandatory for all code generation by AI agents and human developers. Deviations are not permitted unless explicitly approved and documented as an exception in this section or a linked addendum.}

- **Primary Runtime(s):** {e.g., Node.js 22.x, Python Runtime for Lambda - refer to Definitive Tech Stack}
- **Style Guide & Linter:** {e.g., ESLint with Airbnb config + Prettier; Black, Flake8, MyPy; Go fmt, golint. Specify chosen tools and link to configuration files (e.g., `.eslintrc.js`, `pyproject.toml`). Linter rules are mandatory and must not be disabled without cause.}
- **Naming Conventions:**
  - Variables: `{e.g., camelCase (JavaScript/TypeScript/Java), snake_case (Python/Ruby)}`
  - Functions/Methods: `{e.g., camelCase (JavaScript/TypeScript/Java), snake_case (Python/Ruby)}`
  - Classes/Types/Interfaces: `{e.g., PascalCase}`
  - Constants: `{e.g., UPPER_SNAKE_CASE}`
  - Files: `{e.g., kebab-case.ts (TypeScript), snake_case.py (Python), PascalCase.java (Java). Be specific per language.}`
  - Modules/Packages: `{e.g., camelCase or snake_case. Be specific per language.}`
- **File Structure:** Adhere to the layout defined in the "Project Structure" section and the Frontend Architecture Document if applicable.
- **Unit Test File Organization:** {e.g., `*.test.ts`/`*.spec.ts` co-located with source files; `test_*.py` in a parallel `tests/` directory. Specify chosen convention.}
- **Asynchronous Operations:** {e.g., Always use `async`/`await` in TypeScript/JavaScript/Python for promise-based operations; Goroutines/Channels in Go with clear patterns for error propagation and completion; Java `CompletableFuture` or Project Reactor/RxJava if used.}
- **Type Safety:** {e.g., Leverage TypeScript strict mode (all flags enabled); Python type hints (enforced by MyPy); Go static typing; Java generics and avoidance of raw types. All new code must be strictly typed.}
  - _Type Definitions:_ {Location, e.g., `src/common/types.ts`, shared packages, or co-located. Policy on using `any` or equivalent (strongly discouraged, requires justification).}
- **Comments & Documentation:**
  - Code Comments: {Expectations for code comments: Explain _why_, not _what_, for complex logic. Avoid redundant comments. Use standard formats like JSDoc, TSDoc, Python docstrings (Google/NumPy style), GoDoc, JavaDoc.}
  - READMEs: {Each module/package/service should have a README explaining its purpose, setup, and usage if not trivial.}
- **Dependency Management:** {Tool used - e.g., npm/yarn, pip/poetry, Go modules, Maven/Gradle. Policy on adding new dependencies (e.g., approval process, check for existing alternatives, security vulnerability scans). Specify versioning strategy (e.g., prefer pinned versions, use tilde `~` for patches, caret `^` for minor updates - be specific).}

### Detailed Language & Framework Conventions

{For each primary language and framework selected in the "Definitive Tech Stack Selections", the following specific conventions **must** be adhered to. If a chosen technology is not listed below, it implies adherence to its standard, widely accepted best practices and the general guidelines in this document.}

#### `{Language/Framework 1 Name, e.g., TypeScript/Node.js}` Specifics:

- **Immutability:** `{e.g., "Always prefer immutable data structures. Use `Readonly\<T\>`, `ReadonlyArray\<T\>`, `as const` for object/array literals. Avoid direct mutation of objects/arrays passed as props or state. Consider libraries like Immer for complex state updates."}`
- **Functional vs. OOP:** `{e.g., "Favor functional programming constructs (map, filter, reduce, pure functions) for data transformation and business logic where practical. Use classes for entities, services with clear state/responsibilities, or when framework conventions (e.g., NestJS) demand."}`
- **Error Handling Specifics:** `{e.g., "Always use `Error`objects or extensions thereof for`throw`. Ensure `Promise`rejections are always`Error`objects. Use custom error classes inheriting from a base`AppError` for domain-specific errors."}`
- **Null/Undefined Handling:** `{e.g., "Strict null checks (`strictNullChecks`) must be enabled. Avoid `\!` non-null assertion operator; prefer explicit checks, optional chaining (`?.`), or nullish coalescing (`??`). Define clear strategies for optional function parameters and return types."}`
- **Module System:** `{e.g., "Use ESModules (`import`/`export`) exclusively. Avoid CommonJS (`require`/`module.exports`) in new code."}`
- **Logging Specifics:** `{e.g., "Use the chosen structured logging library. Log messages must include a correlation ID. Do not log sensitive PII. Use appropriate log levels."}`
- **Framework Idioms (e.g., for NestJS/Express):** `{e.g., "NestJS: Always use decorators for defining modules, controllers, services, DTOs. Adhere strictly to the defined module structure and dependency injection patterns. Express: Define middleware patterns, routing structure."}`
- **Key Library Usage Conventions:** `{e.g., "When using Axios, create a single configured instance. For date/time, use {date-fns/Luxon/Day.js} and avoid native `Date` object for manipulations."}`
- **Code Generation Anti-Patterns to Avoid:** `{e.g., "Avoid overly nested conditional logic (max 2-3 levels). Avoid single-letter variable names (except for trivial loop counters like `i`, `j`, `k`). Do not write code that bypasses framework security features (e.g., ORM query builders)."}`

#### `{Language/Framework 2 Name, e.g., Python}` Specifics:

- **Immutability:** `{e.g., "Use tuples for immutable sequences. For classes, consider `@dataclass(frozen=True)`. Be mindful of mutable default arguments."}`
- **Functional vs. OOP:** `{e.g., "Employ classes for representing entities and services. Use functions for stateless operations. List comprehensions/generator expressions are preferred over `map/filter` for readability."}`
- **Error Handling Specifics:** `{e.g., "Always raise specific, custom exceptions inheriting from a base `AppException`. Use `try-except-else-finally`blocks appropriately. Avoid broad`except Exception:` clauses without re-raising or specific handling."}`
- **Resource Management:** `{e.g., "Always use `with` statements for resources like files or DB connections to ensure they are properly closed."}`
- **Type Hinting:** `{e.g., "All new functions and methods must have full type hints. Run MyPy in CI. Strive for `disallow_untyped_defs = True`."}`
- **Logging Specifics:** `{e.g., "Use the `logging`module configured for structured output (e.g., with`python-json-logger`). Include correlation IDs."}`
- **Framework Idioms (e.g., for Django/Flask/FastAPI):** `{e.g., "Django: Follow fat models, thin views pattern. Use ORM conventions. FastAPI: Utilize Pydantic for request/response models and dependency injection for services."}`
- **Key Library Usage Conventions:** `{e.g., "For HTTP requests, use `httpx`or`requests`with explicit timeout settings. For data manipulation, prefer`pandas` where appropriate but be mindful of performance."}`

#### `{Add more Language/Framework sections as needed...}`

- **{Consider other things that the trained LLM Dev Agent could potentially be random about specific to the chosen language technologies and platforms that it should be reminded of here}**

## Overall Testing Strategy

{This section outlines the project's comprehensive testing strategy, which all AI-generated and human-written code must adhere to. It complements the testing tools listed in the "Definitive Tech Stack Selections".}

- **Tools:** {Reiterate primary testing frameworks and libraries from Tech Stack, e.g., Jest, Playwright, PyTest, JUnit, Testcontainers.}
- **Unit Tests:**
  - **Scope:** {Test individual functions, methods, classes, or small modules in isolation. Focus on business logic, algorithms, and transformation rules.}
  - **Location:** {e.g., `*.test.ts`/`*.spec.ts` co-located with source files; `test_*.py` in a parallel `tests/` directory, following language conventions.}
  - **Mocking/Stubbing:** {Specify chosen mocking library (e.g., Jest mocks, `unittest.mock` in Python, Mockito for Java). Mock all external dependencies (network calls, file system, databases, time).}
  - **AI Agent Responsibility:** {AI Agent must generate unit tests covering all public methods, significant logic paths, edge cases, and error conditions for any new or modified code.}
- **Integration Tests:**
  - **Scope:** {Test the interaction between several components or services within the application boundary. E.g., API endpoint to service layer to database (using a test database or in-memory version).}
  - **Location:** {e.g., `/tests/integration` or `/src/integration-test` (Java).}
  - **Environment:** {Specify how dependencies are handled (e.g., Testcontainers for databases/external services, in-memory databases, dedicated test environment).}
  - **AI Agent Responsibility:** {AI Agent may be tasked with generating integration tests for key service interactions or API endpoints based on specifications.}
- **End-to-End (E2E) Tests:**
  - **Scope:** {Validate complete user flows or critical paths through the system from the user's perspective (e.g., UI interaction, API call sequence).}
  - **Tools:** {Reiterate E2E testing tools from Tech Stack (e.g., Playwright, Cypress, Selenium).}
  - **AI Agent Responsibility:** {AI Agent may be tasked with generating E2E test stubs or scripts based on user stories or BDD scenarios. Focus on critical happy paths and key error scenarios.}
- **Test Coverage:**
  - **Target:** {Specify target code coverage if any (e.g., 80% line/branch coverage for unit tests). This is a guideline; quality of tests is paramount over raw coverage numbers.}
  - **Measurement:** {Tool used for coverage reports (e.g., Istanbul/nyc, Coverage.py, JaCoCo).}
- **Mocking/Stubbing Strategy (General):** {Beyond specific test types, outline general principles. e.g., "Prefer fakes or test doubles over extensive mocking where it improves test clarity and maintainability. Strive for tests that are fast, reliable, and isolated."}
- **Test Data Management:** {How is test data created, managed, and isolated? E.g., factories, fixtures, setup/teardown scripts, dedicated test data generation tools.}

## Security Best Practices

{Outline key security considerations relevant to the codebase. These are mandatory and must be actively addressed by the AI agent during development.}

- **Input Sanitization/Validation:** {Specify library/method for ALL external inputs (API requests, user-provided data, file uploads). E.g., 'Use class-validator with NestJS DTOs for all API inputs; all validation rules must be defined in DTOs.' For other languages, 'Use {validation_library} for all external inputs; define schemas and constraints.' Validation must occur at the boundary before processing.}
- **Output Encoding:** {Specify where and how output encoding should be performed to prevent XSS and other injection attacks. E.g., 'All dynamic data rendered in HTML templates must be contextually auto-escaped by the template engine (specify engine and confirm default behavior). If generating HTML/XML/JSON manually, use approved encoding libraries like {encoder_library_name}.'}
- **Secrets Management:** {Reference `docs/environment-vars.md` regarding storage for different environments. In code, access secrets _only_ through a designated configuration module/service. Never hardcode secrets, include them in source control, or log them. Use specific tools for local development if applicable (e.g., Doppler, .env files NOT committed).}
- **Dependency Security:** {Policy on checking for vulnerable dependencies. E.g., 'Run automated vulnerability scans (e.g., `npm audit`, `pip-audit`, Snyk, Dependabot alerts) as part of CI. Update vulnerable dependencies promptly based on severity.' Policy on adding new dependencies (vetting process).}
- **Authentication/Authorization Checks:** {Where and how should these be enforced? E.g., 'All API endpoints (except explicitly public ones) must enforce authentication using the central auth module/middleware. Authorization (permission/role checks) must be performed at the service layer or entry point for protected resources.' Define patterns for implementing these checks.}
- **Principle of Least Privilege (Implementation):** {e.g., 'Database connection users must have only the necessary permissions (SELECT, INSERT, UPDATE, DELETE) for the specific tables/schemas they access. IAM roles for cloud services must be narrowly scoped to the required actions and resources.'}
- **API Security (General):** {e.g., 'Enforce HTTPS. Implement rate limiting and throttling (specify tool/method). Use standard HTTP security headers (CSP, HSTS, X-Frame-Options, etc. - specify which ones and their configuration). Follow REST/GraphQL security best practices.'}
- **Error Handling & Information Disclosure:** {Ensure error messages do not leak sensitive information (stack traces, internal paths, detailed SQL errors) to the end-user. Log detailed errors server-side, provide generic messages or error IDs to the client.}
- **Regular Security Audits/Testing:** {Mention if planned, e.g., penetration testing, static/dynamic analysis tool usage in CI (SAST/DAST).}
- **{Other relevant practices, e.g., File upload security, Session management security, Data encryption at rest and in transit beyond HTTPS if specific requirements exist.}**

## Key Reference Documents

{ if any }

## Change Log

| Change | Date | Version | Description | Author |
| ------ | ---- | ------- | ----------- | ------ |

--- Below, Prompt for Design Architect (If Project has UI) To Produce Front End Architecture ----

==================== END: architecture-tmpl ====================


==================== START: design-system-integration-tmpl ====================
# Design System Integration Template

## Document Information
- **Document Type**: Design System Integration Strategy
- **Version**: 1.0
- **Status**: [Draft/Review/Approved]
- **Author**: [Design Architect Name]
- **Date**: [Creation Date]
- **Last Updated**: [Last Update Date]

## Executive Summary

### Integration Overview
- **Project Name**: [Project Name]
- **Design System**: [Design System Name/Version]
- **Microfrontend Count**: [Number of microfrontends]
- **Technology Stack**: [React, Next.js, Tailwind CSS, etc.]
- **Integration Pattern**: [Centralized/Federated/Hybrid]

### Key Objectives
- **Consistency**: Unified user experience across all microfrontends
- **Efficiency**: Reduced design and development time
- **Maintainability**: Centralized design token and component management
- **Scalability**: Support for growing number of microfrontends
- **Accessibility**: WCAG 2.1 AA compliance across all components

## Design System Architecture

### Design Token Hierarchy
```
Global Tokens (Brand Level)
├── Colors
│   ├── Primary: #007bff
│   ├── Secondary: #6c757d
│   ├── Success: #28a745
│   ├── Warning: #ffc107
│   └── Error: #dc3545
├── Typography
│   ├── Font Family: 'Inter', sans-serif
│   ├── Font Sizes: 12px, 14px, 16px, 18px, 24px, 32px
│   ├── Font Weights: 400, 500, 600, 700
│   └── Line Heights: 1.2, 1.4, 1.6, 1.8
├── Spacing
│   ├── Base Unit: 4px
│   ├── Scale: 4px, 8px, 12px, 16px, 24px, 32px, 48px, 64px
│   └── Component Spacing: 8px, 16px, 24px
└── Breakpoints
    ├── Mobile: 320px
    ├── Tablet: 768px
    ├── Desktop: 1024px
    └── Large: 1440px

Semantic Tokens (Context Level)
├── Surface Colors
│   ├── Background: var(--color-neutral-50)
│   ├── Surface: var(--color-neutral-100)
│   └── Overlay: var(--color-neutral-900)
├── Text Colors
│   ├── Primary: var(--color-neutral-900)
│   ├── Secondary: var(--color-neutral-600)
│   └── Disabled: var(--color-neutral-400)
└── Interactive Colors
    ├── Link: var(--color-primary-600)
    ├── Link Hover: var(--color-primary-700)
    └── Focus: var(--color-primary-500)

Component Tokens (Implementation Level)
├── Button
│   ├── Primary Background: var(--color-primary-600)
│   ├── Primary Text: var(--color-neutral-50)
│   ├── Secondary Background: var(--color-neutral-100)
│   └── Secondary Text: var(--color-neutral-900)
├── Input
│   ├── Background: var(--color-neutral-50)
│   ├── Border: var(--color-neutral-300)
│   ├── Focus Border: var(--color-primary-500)
│   └── Error Border: var(--color-error-500)
└── Card
    ├── Background: var(--color-neutral-50)
    ├── Border: var(--color-neutral-200)
    └── Shadow: 0 1px 3px rgba(0, 0, 0, 0.1)
```

### Component Library Structure
```
Design System Package
├── Tokens
│   ├── colors.json
│   ├── typography.json
│   ├── spacing.json
│   └── breakpoints.json
├── Components
│   ├── Primitives
│   │   ├── Button
│   │   ├── Input
│   │   ├── Text
│   │   └── Icon
│   ├── Composites
│   │   ├── Card
│   │   ├── Modal
│   │   ├── Navigation
│   │   └── Form
│   └── Layouts
│       ├── Container
│       ├── Grid
│       ├── Stack
│       └── Flex
├── Themes
│   ├── light.json
│   ├── dark.json
│   └── high-contrast.json
└── Documentation
    ├── Storybook
    ├── Usage Guidelines
    └── Migration Guides
```

## Integration Strategies

### Centralized Distribution
```typescript
// Design System Package Structure
interface DesignSystemPackage {
  tokens: DesignTokens;
  components: ComponentLibrary;
  themes: ThemeConfiguration;
  utilities: UtilityFunctions;
  documentation: Documentation;
}

// Package.json for design system
{
  "name": "@company/design-system",
  "version": "2.1.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "exports": {
    ".": "./dist/index.js",
    "./tokens": "./dist/tokens/index.js",
    "./components": "./dist/components/index.js",
    "./themes": "./dist/themes/index.js"
  },
  "peerDependencies": {
    "react": ">=18.0.0",
    "react-dom": ">=18.0.0"
  }
}
```

### Microfrontend Integration
```typescript
// Microfrontend integration example
import { 
  DesignSystemProvider,
  Button,
  Card,
  useTheme,
  tokens 
} from '@company/design-system';

// App-level integration
function App() {
  return (
    <DesignSystemProvider theme="light">
      <MicrofrontendContent />
    </DesignSystemProvider>
  );
}

// Component usage
function MicrofrontendContent() {
  const theme = useTheme();
  
  return (
    <Card>
      <Button variant="primary" size="medium">
        Action Button
      </Button>
    </Card>
  );
}
```

### Token Integration with Tailwind CSS
```javascript
// tailwind.config.js
const designTokens = require('@company/design-system/tokens');

module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: designTokens.colors,
      fontFamily: designTokens.typography.fontFamily,
      fontSize: designTokens.typography.fontSize,
      spacing: designTokens.spacing,
      screens: designTokens.breakpoints,
    },
  },
  plugins: [
    require('@company/design-system/tailwind-plugin'),
  ],
};
```

## Component Development Standards

### Component API Design
```typescript
// Standard component interface
interface ComponentProps {
  // Visual variants
  variant?: 'primary' | 'secondary' | 'tertiary';
  size?: 'small' | 'medium' | 'large';
  
  // State props
  disabled?: boolean;
  loading?: boolean;
  error?: boolean;
  
  // Accessibility props
  'aria-label'?: string;
  'aria-describedby'?: string;
  id?: string;
  
  // Event handlers
  onClick?: (event: MouseEvent) => void;
  onFocus?: (event: FocusEvent) => void;
  onBlur?: (event: FocusEvent) => void;
  
  // Styling props
  className?: string;
  style?: CSSProperties;
  
  // Content
  children?: ReactNode;
}

// Example Button component
interface ButtonProps extends ComponentProps {
  type?: 'button' | 'submit' | 'reset';
  href?: string; // For link buttons
  target?: string;
  rel?: string;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant = 'primary', size = 'medium', ...props }, ref) => {
    const classes = cn(
      'btn',
      `btn--${variant}`,
      `btn--${size}`,
      props.disabled && 'btn--disabled',
      props.loading && 'btn--loading',
      props.className
    );
    
    return (
      <button
        ref={ref}
        className={classes}
        {...props}
      >
        {props.loading && <Spinner size="small" />}
        {props.children}
      </button>
    );
  }
);
```

### Accessibility Standards
```typescript
// Accessibility utilities
interface A11yProps {
  role?: string;
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-hidden'?: boolean;
  'aria-live'?: 'polite' | 'assertive' | 'off';
  tabIndex?: number;
}

// Focus management hook
const useFocusManagement = () => {
  const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
  
  const trapFocus = (container: HTMLElement) => {
    const focusable = container.querySelectorAll(focusableElements);
    const firstFocusable = focusable[0] as HTMLElement;
    const lastFocusable = focusable[focusable.length - 1] as HTMLElement;
    
    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstFocusable) {
            lastFocusable.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastFocusable) {
            firstFocusable.focus();
            e.preventDefault();
          }
        }
      }
    };
    
    container.addEventListener('keydown', handleTabKey);
    return () => container.removeEventListener('keydown', handleTabKey);
  };
  
  return { trapFocus };
};
```

## Theme Management

### Theme Configuration
```typescript
// Theme interface
interface Theme {
  name: string;
  colors: ColorPalette;
  typography: TypographyScale;
  spacing: SpacingScale;
  shadows: ShadowScale;
  borderRadius: BorderRadiusScale;
  transitions: TransitionScale;
}

// Light theme
const lightTheme: Theme = {
  name: 'light',
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      500: '#3b82f6',
      600: '#2563eb',
      900: '#1e3a8a',
    },
    neutral: {
      50: '#f9fafb',
      100: '#f3f4f6',
      500: '#6b7280',
      900: '#111827',
    },
  },
  // ... other theme properties
};

// Dark theme
const darkTheme: Theme = {
  name: 'dark',
  colors: {
    primary: {
      50: '#1e3a8a',
      100: '#2563eb',
      500: '#3b82f6',
      600: '#dbeafe',
      900: '#eff6ff',
    },
    neutral: {
      50: '#111827',
      100: '#1f2937',
      500: '#9ca3af',
      900: '#f9fafb',
    },
  },
  // ... other theme properties
};
```

### Theme Provider Implementation
```typescript
// Theme context
const ThemeContext = createContext<{
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}>({
  theme: lightTheme,
  setTheme: () => {},
  toggleTheme: () => {},
});

// Theme provider component
export const DesignSystemProvider: React.FC<{
  children: ReactNode;
  defaultTheme?: Theme;
}> = ({ children, defaultTheme = lightTheme }) => {
  const [theme, setTheme] = useState(defaultTheme);
  
  const toggleTheme = useCallback(() => {
    setTheme(current => current.name === 'light' ? darkTheme : lightTheme);
  }, []);
  
  // Apply CSS custom properties
  useEffect(() => {
    const root = document.documentElement;
    Object.entries(theme.colors).forEach(([key, value]) => {
      if (typeof value === 'object') {
        Object.entries(value).forEach(([shade, color]) => {
          root.style.setProperty(`--color-${key}-${shade}`, color);
        });
      } else {
        root.style.setProperty(`--color-${key}`, value);
      }
    });
  }, [theme]);
  
  return (
    <ThemeContext.Provider value={{ theme, setTheme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Theme hook
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a DesignSystemProvider');
  }
  return context;
};
```

## Responsive Design Strategy

### Breakpoint Management
```typescript
// Responsive utilities
const breakpoints = {
  mobile: '320px',
  tablet: '768px',
  desktop: '1024px',
  large: '1440px',
} as const;

// Media query hook
const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);
  
  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    
    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);
    return () => media.removeEventListener('change', listener);
  }, [matches, query]);
  
  return matches;
};

// Responsive component example
const ResponsiveGrid: React.FC<{
  children: ReactNode;
  columns?: { mobile?: number; tablet?: number; desktop?: number };
}> = ({ children, columns = { mobile: 1, tablet: 2, desktop: 3 } }) => {
  const isMobile = useMediaQuery(`(max-width: ${breakpoints.tablet})`);
  const isTablet = useMediaQuery(`(min-width: ${breakpoints.tablet}) and (max-width: ${breakpoints.desktop})`);
  
  const currentColumns = isMobile 
    ? columns.mobile 
    : isTablet 
    ? columns.tablet 
    : columns.desktop;
  
  return (
    <div 
      className="grid gap-4"
      style={{ gridTemplateColumns: `repeat(${currentColumns}, 1fr)` }}
    >
      {children}
    </div>
  );
};
```

## Testing Strategy

### Component Testing
```typescript
// Component test example
import { render, screen, fireEvent } from '@testing-library/react';
import { DesignSystemProvider } from '@company/design-system';
import { Button } from './Button';

const renderWithProvider = (component: ReactElement) => {
  return render(
    <DesignSystemProvider>
      {component}
    </DesignSystemProvider>
  );
};

describe('Button Component', () => {
  it('renders with correct variant styles', () => {
    renderWithProvider(<Button variant="primary">Click me</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('btn--primary');
  });
  
  it('handles click events', () => {
    const handleClick = jest.fn();
    renderWithProvider(
      <Button onClick={handleClick}>Click me</Button>
    );
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  it('is accessible', async () => {
    renderWithProvider(<Button aria-label="Submit form">Submit</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveAccessibleName('Submit form');
  });
});
```

### Visual Regression Testing
```typescript
// Storybook stories for visual testing
import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'tertiary'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Button',
  },
};

export const AllVariants: Story = {
  render: () => (
    <div className="flex gap-4">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="tertiary">Tertiary</Button>
    </div>
  ),
};
```

## Documentation Strategy

### Component Documentation
```typescript
// Component documentation template
/**
 * Button component for user interactions
 * 
 * @example
 * ```tsx
 * <Button variant="primary" size="medium" onClick={handleClick}>
 *   Click me
 * </Button>
 * ```
 */
export interface ButtonProps {
  /** Visual style variant */
  variant?: 'primary' | 'secondary' | 'tertiary';
  /** Size of the button */
  size?: 'small' | 'medium' | 'large';
  /** Disabled state */
  disabled?: boolean;
  /** Loading state with spinner */
  loading?: boolean;
  /** Click event handler */
  onClick?: (event: MouseEvent<HTMLButtonElement>) => void;
  /** Button content */
  children: ReactNode;
}
```

### Usage Guidelines
```markdown
## Button Usage Guidelines

### When to Use
- Primary actions (save, submit, continue)
- Secondary actions (cancel, back)
- Tertiary actions (learn more, view details)

### When Not to Use
- Navigation between pages (use Link instead)
- Toggling states (use Toggle or Switch)
- Multiple selection (use Checkbox)

### Accessibility
- Always provide meaningful text or aria-label
- Use appropriate button type (button, submit, reset)
- Ensure sufficient color contrast (4.5:1 minimum)
- Provide focus indicators
- Support keyboard navigation

### Best Practices
- Use clear, action-oriented labels
- Limit to one primary button per section
- Group related actions together
- Consider loading states for async actions
```

## Migration Strategy

### Version Management
```json
{
  "migration_guide": {
    "from_version": "1.x",
    "to_version": "2.x",
    "breaking_changes": [
      {
        "component": "Button",
        "change": "Renamed 'type' prop to 'variant'",
        "migration": "Replace 'type' with 'variant' in all Button components"
      }
    ],
    "new_features": [
      {
        "component": "Card",
        "feature": "Added elevation prop for shadow variants"
      }
    ],
    "deprecated_features": [
      {
        "component": "OldButton",
        "deprecation_date": "2024-06-01",
        "removal_date": "2024-12-01",
        "replacement": "Button"
      }
    ]
  }
}
```

### Automated Migration Tools
```typescript
// Codemod for automated migration
import { Transform } from 'jscodeshift';

const transform: Transform = (fileInfo, api) => {
  const j = api.jscodeshift;
  const root = j(fileInfo.source);
  
  // Migrate Button type prop to variant
  root
    .find(j.JSXElement, {
      openingElement: {
        name: { name: 'Button' }
      }
    })
    .find(j.JSXAttribute, {
      name: { name: 'type' }
    })
    .forEach(path => {
      path.node.name.name = 'variant';
    });
  
  return root.toSource();
};

export default transform;
```

## Governance and Maintenance

### Design System Governance
- **Design Review Board**: Cross-functional team for design decisions
- **Component Ownership**: Clear ownership and maintenance responsibilities
- **Contribution Process**: Guidelines for proposing new components
- **Breaking Change Policy**: Process for managing breaking changes
- **Release Schedule**: Regular release cadence and versioning strategy

### Quality Assurance
- **Automated Testing**: Unit, integration, and visual regression tests
- **Accessibility Audits**: Regular WCAG compliance validation
- **Performance Monitoring**: Bundle size and runtime performance tracking
- **Usage Analytics**: Component adoption and usage patterns
- **Feedback Collection**: User feedback and improvement suggestions

---

*This template provides a comprehensive framework for design system integration in microfrontend architectures. Customize based on your specific design system and organizational requirements.*

==================== END: design-system-integration-tmpl ====================


==================== START: doc-sharding-tmpl ====================
# Document Sharding Plan Template

This plan directs the agent on how to break down large source documents into smaller, granular files during its Librarian Phase. The agent will refer to this plan to identify source documents, the specific sections to extract, and the target filenames for the sharded content.

---

## 1. Source Document: PRD (Project Requirements Document)

- **Note to Agent:** Confirm the exact filename of the PRD with the user (e.g., `PRD.md`, `ProjectRequirements.md`, `prdx.y.z.md`).

### 1.1. Epic Granulation

- **Instruction:** For each Epic identified within the PRD:
- **Source Section(s) to Copy:** The complete text for the Epic, including its main description, goals, and all associated user stories or detailed requirements under that Epic. Ensure to capture content starting from a heading like "**Epic X:**" up to the next such heading or end of the "Epic Overview" section.
- **Target File Pattern:** `docs/epic-<id>.md`
  - _Agent Note: `<id>` should correspond to the Epic number._

---

## 2. Source Document: Main Architecture Document

- **Note to Agent:** Confirm the exact filename with the user (e.g., `architecture.md`, `SystemArchitecture.md`).

### 2.1. Core Architecture Granules

- **Source Section(s) to Copy:** Section(s) detailing "API Reference", "API Endpoints", or "Service Interfaces".
- **Target File:** `docs/api-reference.md`

- **Source Section(s) to Copy:** Section(s) detailing "Data Models", "Database Schema", "Entity Definitions".
- **Target File:** `docs/data-models.md`

- **Source Section(s) to Copy:** Section(s) titled "Environment Variables Documentation", "Configuration Settings", "Deployment Parameters", or relevant subsections within "Infrastructure and Deployment Overview" if a dedicated section is not found.
- **Target File:** `docs/environment-vars.md`

  - _Agent Note: Prioritize a dedicated 'Environment Variables' section or linked 'environment-vars.md' source if available. If not, extract relevant configuration details from 'Infrastructure and Deployment Overview'. This shard is for specific variable definitions and usage._

- **Source Section(s) to Copy:** Section(s) detailing "Project Structure".
- **Target File:** `docs/project-structure.md`

  - _Agent Note: If the project involves multiple repositories (not a monorepo), ensure this file clearly describes the structure of each relevant repository or links to sub-files if necessary._

- **Source Section(s) to Copy:** Section(s) detailing "Technology Stack", "Key Technologies", "Libraries and Frameworks", or "Definitive Tech Stack Selections".
- **Target File:** `docs/tech-stack.md`

- **Source Section(s) to Copy:** Sections detailing "Coding Standards", "Development Guidelines", "Best Practices", "Testing Strategy", "Testing Decisions", "QA Processes", "Overall Testing Strategy", "Error Handling Strategy", and "Security Best Practices".
- **Target File:** `docs/operational-guidelines.md`

  - _Agent Note: This file consolidates several key operational aspects. Ensure that the content from each source section ("Coding Standards", "Testing Strategy", "Error Handling Strategy", "Security Best Practices") is clearly delineated under its own H3 (###) or H4 (####) heading within this document._

- **Source Section(s) to Copy:** Section(s) titled "Component View" (including sub-sections like "Architectural / Design Patterns Adopted").
- **Target File:** `docs/component-view.md`

- **Source Section(s) to Copy:** Section(s) titled "Core Workflow / Sequence Diagrams" (including all sub-diagrams).
- **Target File:** `docs/sequence-diagrams.md`

- **Source Section(s) to Copy:** Section(s) titled "Infrastructure and Deployment Overview".
- **Target File:** `docs/infra-deployment.md`

  - _Agent Note: This is for the broader overview, distinct from the specific `docs/environment-vars.md`._

- **Source Section(s) to Copy:** Section(s) titled "Key Reference Documents".
- **Target File:** `docs/key-references.md`

---

## 3. Source Document(s): Front-End Specific Documentation

- **Note to Agent:** Confirm filenames with the user (e.g., `front-end-architecture.md`, `front-end-spec.md`, `ui-guidelines.md`). Multiple FE documents might exist.

### 3.1. Front-End Granules

- **Source Section(s) to Copy:** Section(s) detailing "Front-End Project Structure" or "Detailed Frontend Directory Structure".
- **Target File:** `docs/front-end-project-structure.md`

- **Source Section(s) to Copy:** Section(s) detailing "UI Style Guide", "Brand Guidelines", "Visual Design Specifications", or "Styling Approach".
- **Target File:** `docs/front-end-style-guide.md`

  - _Agent Note: This section might be a sub-section or refer to other documents (e.g., `ui-ux-spec.txt`). Extract the core styling philosophy and approach defined within the frontend architecture document itself._

- **Source Section(s) to Copy:** Section(s) detailing "Component Library", "Reusable UI Components Guide", "Atomic Design Elements", or "Component Breakdown & Implementation Details".
- **Target File:** `docs/front-end-component-guide.md`

- **Source Section(s) to Copy:** Section(s) detailing "Front-End Coding Standards" (specifically for UI development, e.g., JavaScript/TypeScript style, CSS naming conventions, accessibility best practices for FE).
- **Target File:** `docs/front-end-coding-standards.md`

  - _Agent Note: A dedicated top-level section for this might not exist. If not found, this shard might be empty or require cross-referencing with the main architecture's coding standards. Extract any front-end-specific coding conventions mentioned._

- **Source Section(s) to Copy:** Section(s) titled "State Management In-Depth".
- **Target File:** `docs/front-end-state-management.md`

- **Source Section(s) to Copy:** Section(s) titled "API Interaction Layer".
- **Target File:** `docs/front-end-api-interaction.md`

- **Source Section(s) to Copy:** Section(s) titled "Routing Strategy".
- **Target File:** `docs/front-end-routing-strategy.md`

- **Source Section(s) to Copy:** Section(s) titled "Frontend Testing Strategy".
- **Target File:** `docs/front-end-testing-strategy.md`

---

CRITICAL: **Index Management:** After creating the files, update `docs/index.md` as needed to reference and describe each doc - do not mention granules or where it was sharded from, just doc purpose - as the index also contains other doc references potentially.

==================== END: doc-sharding-tmpl ====================


==================== START: event-schema-definition-tmpl ====================
# Event Schema Definition: {Event Name}
## Event-Driven Architecture Communication Contract

### Document Information
- **Event Name:** {EventName}
- **Event Type:** {EventType} (e.g., UserCreated, OrderProcessed, PaymentCompleted)
- **Schema Version:** 1.0
- **Publishing Service:** {Service Name}
- **Creation Date:** {Date}
- **Owner Team:** {Team Name}
- **Last Updated:** {Date}

---

## 1. Event Overview

### Business Purpose
{Clear description of what business event this represents and why it's published}

### Event Category
- [ ] **Domain Event** - Represents something that happened in the business domain
- [ ] **Integration Event** - Facilitates communication between bounded contexts
- [ ] **System Event** - Technical event for system operations
- [ ] **Notification Event** - Informational event for notifications

### Event Trigger
{What business action or system condition triggers this event}

### Expected Consumers
{List of services or systems that are expected to consume this event}

---

## 2. Event Schema Specification

### Core Event Structure
```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "type": "object",
  "title": "{EventName}",
  "description": "{Event description}",
  "required": [
    "event_id",
    "event_type",
    "event_version",
    "timestamp",
    "source_service",
    "correlation_id",
    "data"
  ],
  "properties": {
    "event_id": {
      "type": "string",
      "format": "uuid",
      "description": "Unique identifier for this event instance"
    },
    "event_type": {
      "type": "string",
      "const": "{EventType}",
      "description": "Type of event"
    },
    "event_version": {
      "type": "string",
      "pattern": "^\\d+\\.\\d+$",
      "description": "Schema version (semantic versioning)"
    },
    "timestamp": {
      "type": "string",
      "format": "date-time",
      "description": "When the event occurred (ISO 8601)"
    },
    "source_service": {
      "type": "string",
      "description": "Service that published this event"
    },
    "correlation_id": {
      "type": "string",
      "format": "uuid",
      "description": "ID to correlate related events across services"
    },
    "causation_id": {
      "type": "string",
      "format": "uuid",
      "description": "ID of the command/event that caused this event"
    },
    "data": {
      "type": "object",
      "description": "Event-specific payload",
      "properties": {
        // Event-specific data schema defined below
      }
    },
    "metadata": {
      "type": "object",
      "description": "Additional context information",
      "properties": {
        "tenant_id": {
          "type": "string",
          "description": "Multi-tenant identifier"
        },
        "user_id": {
          "type": "string",
          "description": "User who triggered the event"
        },
        "session_id": {
          "type": "string",
          "description": "User session identifier"
        },
        "trace_id": {
          "type": "string",
          "description": "Distributed tracing identifier"
        }
      }
    }
  }
}
```

### Event-Specific Data Schema
```json
// Define the specific "data" payload for this event type
{
  "data": {
    "type": "object",
    "required": [
      // List required fields
    ],
    "properties": {
      "entity_id": {
        "type": "string",
        "description": "ID of the main entity this event relates to"
      },
      "entity_type": {
        "type": "string",
        "description": "Type of entity (User, Order, Product, etc.)"
      },
      "change_type": {
        "type": "string",
        "enum": ["created", "updated", "deleted", "status_changed"],
        "description": "Type of change that occurred"
      },
      "previous_state": {
        "type": "object",
        "description": "Previous state of the entity (for updates)"
      },
      "current_state": {
        "type": "object",
        "description": "Current state of the entity"
      },
      // Add event-specific fields here
      "custom_field_1": {
        "type": "string",
        "description": "Description of custom field"
      },
      "custom_field_2": {
        "type": "integer",
        "description": "Description of custom field"
      }
    }
  }
}
```

---

## 3. Event Examples

### Example 1: Successful Event
```json
{
  "event_id": "123e4567-e89b-12d3-a456-426614174000",
  "event_type": "{EventType}",
  "event_version": "1.0",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "source_service": "{ServiceName}",
  "correlation_id": "987fcdeb-51a2-43d1-b123-456789abcdef",
  "causation_id": "456e7890-e12b-34d5-a678-901234567890",
  "data": {
    "entity_id": "user_12345",
    "entity_type": "User",
    "change_type": "created",
    "current_state": {
      "id": "user_12345",
      "email": "<EMAIL>",
      "status": "active",
      "created_at": "2024-01-15T10:30:00.000Z"
    },
    "custom_field_1": "example_value",
    "custom_field_2": 42
  },
  "metadata": {
    "tenant_id": "tenant_001",
    "user_id": "admin_user",
    "session_id": "session_abc123",
    "trace_id": "trace_xyz789"
  }
}
```

### Example 2: Update Event
```json
{
  "event_id": "234f5678-f90c-23e4-b567-537725285111",
  "event_type": "{EventType}",
  "event_version": "1.0",
  "timestamp": "2024-01-15T11:45:00.000Z",
  "source_service": "{ServiceName}",
  "correlation_id": "987fcdeb-51a2-43d1-b123-456789abcdef",
  "causation_id": "567f8901-f23c-45e6-b789-012345678901",
  "data": {
    "entity_id": "user_12345",
    "entity_type": "User",
    "change_type": "updated",
    "previous_state": {
      "status": "active",
      "email": "<EMAIL>"
    },
    "current_state": {
      "status": "suspended",
      "email": "<EMAIL>",
      "suspended_at": "2024-01-15T11:45:00.000Z"
    }
  },
  "metadata": {
    "tenant_id": "tenant_001",
    "user_id": "admin_user",
    "session_id": "session_def456",
    "trace_id": "trace_uvw012"
  }
}
```

---

## 4. Publishing Configuration

### Message Broker Configuration
- **Topic/Queue Name:** {topic_name}
- **Partition Strategy:** {How messages are partitioned}
- **Retention Policy:** {How long messages are retained}
- **Replication Factor:** {Number of replicas for reliability}

### Publishing Guarantees
- [ ] **At-Least-Once** - Message delivered at least once (may have duplicates)
- [ ] **At-Most-Once** - Message delivered at most once (may be lost)
- [ ] **Exactly-Once** - Message delivered exactly once (requires special handling)

### Ordering Requirements
- [ ] **No Ordering Required** - Messages can be processed in any order
- [ ] **Partition Ordering** - Messages within same partition are ordered
- [ ] **Global Ordering** - All messages must be processed in order

### Publishing Frequency
- **Expected Volume:** {Events per second/minute/hour}
- **Peak Volume:** {Maximum expected events during peak times}
- **Batch Size:** {Number of events published together}

---

## 5. Consumer Guidelines

### Processing Requirements
- **Idempotency:** Consumers must handle duplicate events gracefully
- **Error Handling:** Failed events should be retried with exponential backoff
- **Dead Letter Queue:** Failed events after max retries go to DLQ
- **Processing Timeout:** Maximum time to process an event

### Consumer Registration
{How consumers register to receive this event type}

### Filtering Options
{Available filtering options for consumers (if any)}

### Acknowledgment Requirements
{How consumers should acknowledge successful processing}

---

## 6. Backward Compatibility

### Schema Evolution Rules
- **Additive Changes:** New optional fields can be added
- **Field Removal:** Fields cannot be removed (mark as deprecated)
- **Type Changes:** Field types cannot be changed
- **Required Fields:** New required fields break compatibility

### Versioning Strategy
- **Major Version:** Breaking changes require major version bump
- **Minor Version:** Backward-compatible additions
- **Patch Version:** Bug fixes and clarifications

### Deprecation Policy
- **Deprecation Notice:** 6 months advance notice for breaking changes
- **Support Period:** Old versions supported for 12 months
- **Migration Guide:** Provided for all breaking changes

---

## 7. Monitoring and Observability

### Key Metrics
- **Publishing Rate:** Events published per second
- **Processing Latency:** Time from publish to consumption
- **Error Rate:** Percentage of failed event processing
- **Consumer Lag:** How far behind consumers are

### Alerting Rules
- **High Error Rate:** Alert when error rate > {threshold}%
- **High Latency:** Alert when processing time > {threshold}ms
- **Consumer Lag:** Alert when lag > {threshold} messages
- **Publishing Failures:** Alert on publishing failures

### Distributed Tracing
- **Trace Propagation:** How trace context is included in events
- **Span Creation:** What operations create spans
- **Correlation:** How events are correlated across services

---

## 8. Security and Privacy

### Data Classification
- [ ] **Public** - No sensitive information
- [ ] **Internal** - Company internal information
- [ ] **Confidential** - Sensitive business information
- [ ] **Restricted** - Highly sensitive or regulated data

### PII Handling
{How personally identifiable information is handled in events}

### Encryption Requirements
- [ ] **Encryption in Transit** - Events encrypted during transmission
- [ ] **Encryption at Rest** - Events encrypted in storage
- [ ] **Field-Level Encryption** - Specific fields encrypted

### Access Control
{Who can publish and consume this event type}

---

## 9. Testing Strategy

### Schema Validation Testing
- **Valid Schema Tests:** Verify events conform to schema
- **Invalid Schema Tests:** Verify invalid events are rejected
- **Evolution Tests:** Test backward compatibility

### Integration Testing
- **End-to-End Tests:** Full publish-consume workflow
- **Consumer Tests:** Verify all consumers handle events correctly
- **Failure Tests:** Test error handling and recovery

### Performance Testing
- **Load Tests:** Verify performance under expected load
- **Stress Tests:** Test behavior under extreme load
- **Latency Tests:** Measure end-to-end processing time

---

## 10. Documentation and Support

### Consumer Documentation
{Link to documentation for event consumers}

### Schema Registry
{Link to schema registry where this schema is stored}

### Support Contacts
- **Technical Owner:** {Name and contact}
- **Business Owner:** {Name and contact}
- **On-Call Support:** {Contact information}

### Related Events
{List of related events that consumers might also be interested in}

---

## 11. Approval and Sign-off

### Technical Review
- **Schema Designer:** {Name} - {Date} - {Signature}
- **Publishing Service Team:** {Name} - {Date} - {Signature}
- **Architecture Review:** {Name} - {Date} - {Signature}

### Business Review
- **Product Owner:** {Name} - {Date} - {Signature}
- **Domain Expert:** {Name} - {Date} - {Signature}

---

## 12. Change Log

| Version | Date | Changes | Author | Approver |
|---------|------|---------|--------|----------|
| 1.0 | {Date} | Initial schema definition | {Author} | {Approver} |

---

## 13. References

- **Event Storming Session:** {Link to session notes}
- **Domain Model:** {Link to domain model documentation}
- **API Documentation:** {Link to related API docs}
- **Architecture Decision Records:** {Link to relevant ADRs}

==================== END: event-schema-definition-tmpl ====================


==================== START: front-end-architecture-tmpl ====================
# {Project Name} Frontend Architecture Document

## Table of Contents

{ Update this if sections and subsections are added or removed }

- [Introduction](#introduction)
- [Overall Frontend Philosophy & Patterns](#overall-frontend-philosophy--patterns)
- [Detailed Frontend Directory Structure](#detailed-frontend-directory-structure)
- [Component Breakdown & Implementation Details](#component-breakdown--implementation-details)
  - [Component Naming & Organization](#component-naming--organization)
  - [Template for Component Specification](#template-for-component-specification)
- [State Management In-Depth](#state-management-in-depth)
  - [Store Structure / Slices](#store-structure--slices)
  - [Key Selectors](#key-selectors)
  - [Key Actions / Reducers / Thunks](#key-actions--reducers--thunks)
- [API Interaction Layer](#api-interaction-layer)
  - [Client/Service Structure](#clientservice-structure)
  - [Error Handling & Retries (Frontend)](#error-handling--retries-frontend)
- [Routing Strategy](#routing-strategy)
  - [Route Definitions](#route-definitions)
  - [Route Guards / Protection](#route-guards--protection)
- [Build, Bundling, and Deployment](#build-bundling-and-deployment)
  - [Build Process & Scripts](#build-process--scripts)
  - [Key Bundling Optimizations](#key-bundling-optimizations)
  - [Deployment to CDN/Hosting](#deployment-to-cdnhosting)
- [Frontend Testing Strategy](#frontend-testing-strategy)
  - [Component Testing](#component-testing)
  - [UI Integration/Flow Testing](#ui-integrationflow-testing)
  - [End-to-End UI Testing Tools & Scope](#end-to-end-ui-testing-tools--scope)
- [Accessibility (AX) Implementation Details](#accessibility-ax-implementation-details)
- [Performance Considerations](#performance-considerations)
- [Internationalization (i18n) and Localization (l10n) Strategy](#internationalization-i18n-and-localization-l10n-strategy)
- [Feature Flag Management](#feature-flag-management)
- [Frontend Security Considerations](#frontend-security-considerations)
- [Browser Support and Progressive Enhancement](#browser-support-and-progressive-enhancement)
- [Change Log](#change-log)

## Introduction

{ This document details the technical architecture specifically for the frontend of {Project Name}. It complements the main {Project Name} Architecture Document and the UI/UX Specification. This document details the frontend architecture and **builds upon the foundational decisions** (e.g., overall tech stack, CI/CD, primary testing tools) defined in the main {Project Name} Architecture Document (`docs/architecture.md` or linked equivalent). **Frontend-specific elaborations or deviations from general patterns must be explicitly noted here.** The goal is to provide a clear blueprint for frontend development, ensuring consistency, maintainability, and alignment with the overall system design and user experience goals. }

- **Link to Main Architecture Document (REQUIRED):** {e.g., `docs/architecture.md`}
- **Link to UI/UX Specification (REQUIRED if exists):** {e.g., `docs/front-end-spec.md`}
- **Link to Primary Design Files (Figma, Sketch, etc.) (REQUIRED if exists):** {From UI/UX Spec}
- **Link to Deployed Storybook / Component Showcase (if applicable):** {URL}

## Overall Frontend Philosophy & Patterns

{ Describe the core architectural decisions and patterns chosen for the frontend. This should align with the "Definitive Tech Stack Selections" in the main architecture document and consider implications from the overall system architecture (e.g., monorepo vs. polyrepo, backend service structure). }

- **Framework & Core Libraries:** {e.g., React 18.x with Next.js 13.x, Angular 16.x, Vue 3.x with Nuxt.js}. **These are derived from the 'Definitive Tech Stack Selections' in the main Architecture Document.** This section elaborates on *how* these choices are applied specifically to the frontend.
- **Component Architecture:** {e.g., Atomic Design principles, Presentational vs. Container components, use of specific component libraries like Material UI, Tailwind CSS for styling approach. Specify chosen approach and any key libraries.}
- **State Management Strategy:** {e.g., Redux Toolkit, Zustand, Vuex, NgRx. Briefly describe the overall approach – global store, feature stores, context API usage. **Referenced from main Architecture Document and detailed further in "State Management In-Depth" section.**}
- **Data Flow:** {e.g., Unidirectional data flow (Flux/Redux pattern), React Query/SWR for server state. Describe how data is fetched, cached, passed to components, and updated.}
- **Styling Approach:** **{Chosen Styling Solution, e.g., Tailwind CSS / CSS Modules / Styled Components}**. Configuration File(s): {e.g., `tailwind.config.js`, `postcss.config.js`}. Key conventions: {e.g., "Utility-first approach for Tailwind. Custom components defined in `src/styles/components.css`. Theme extensions in `tailwind.config.js` under `theme.extend`. For CSS Modules, files are co-located with components, e.g., `MyComponent.module.css`.}
- **Key Design Patterns Used:** {e.g., Provider pattern, Hooks, Higher-Order Components, Service patterns for API calls, Container/Presentational. These patterns are to be consistently applied. Deviations require justification and documentation.}

## Detailed Frontend Directory Structure

{ Provide an ASCII diagram representing the frontend application\'s specific folder structure (e.g., within `src/` or `app/` or a dedicated `frontend/` root directory if part of a monorepo). This should elaborate on the frontend part of the main project structure outlined in the architecture document. Highlight conventions for organizing components, pages/views, services, state, styles, assets, etc. For each key directory, provide a one-sentence mandatory description of its purpose.}

### EXAMPLE - Not Prescriptive (for a React/Next.js app)

```plaintext
src/
├── app/                        # Next.js App Router: Pages/Layouts/Routes. MUST contain route segments, layouts, and page components.
│   ├── (features)/             # Feature-based routing groups. MUST group related routes for a specific feature.
│   │   └── dashboard/
│   │       ├── layout.tsx      # Layout specific to the dashboard feature routes.
│   │       └── page.tsx        # Entry page component for a dashboard route.
│   ├── api/                    # API Routes (if using Next.js backend features). MUST contain backend handlers for client-side calls.
│   ├── globals.css             # Global styles. MUST contain base styles, CSS variable definitions, Tailwind base/components/utilities.
│   └── layout.tsx              # Root layout for the entire application.
├── components/                 # Shared/Reusable UI Components.
│   ├── ui/                     # Base UI elements (Button, Input, Card). MUST contain only generic, reusable, presentational UI elements, often mapped from a design system. MUST NOT contain business logic.
│   │   ├── Button.tsx
│   │   └── ...
│   ├── layout/                 # Layout components (Header, Footer, Sidebar). MUST contain components structuring page layouts, not specific page content.
│   │   ├── Header.tsx
│   │   └── ...
│   └── (feature-specific)/     # Components specific to a feature but potentially reusable within it. This is an alternative to co-locating within features/ directory.
│       └── user-profile/
│           └── ProfileCard.tsx
├── features/                   # Feature-specific logic, hooks, non-global state, services, and components solely used by that feature.
│   └── auth/
│       ├── components/         # Components used exclusively by the auth feature. MUST NOT be imported by other features.
│       ├── hooks/              # Custom React Hooks specific to the 'auth' feature. Hooks reusable across features belong in `src/hooks/`.
│       ├── services/           # Feature-specific API interactions or orchestrations for the 'auth' feature.
│       └── store.ts            # Feature-specific state slice (e.g., Redux slice) if not part of a global store or if local state is complex.
├── hooks/                      # Global/sharable custom React Hooks. MUST be generic and usable by multiple features/components.
│   └── useAuth.ts
├── lib/ / utils/             # Utility functions, helpers, constants. MUST contain pure functions and constants, no side effects or framework-specific code unless clearly named (e.g., `react-helpers.ts`).
│   └── utils.ts
├── services/                   # Global API service clients or SDK configurations. MUST define base API client instances and core data fetching/mutation services.
│   └── apiClient.ts
├── store/                      # Global state management setup (e.g., Redux store, Zustand store).
│   ├── index.ts                # Main store configuration and export.
│   ├── rootReducer.ts          # Root reducer if using Redux.
│   └── (slices)/               # Directory for global state slices (if not co-located in features).
├── styles/                     # Global styles, theme configurations (if not using `globals.css` or similar, or for specific styling systems like SCSS partials).
└── types/                      # Global TypeScript type definitions/interfaces. MUST contain types shared across multiple features/modules.
    └── index.ts
```

### Notes on Frontend Structure:

{ Explain any specific conventions or rationale behind the structure. e.g., "Components are co-located with their feature if not globally reusable to improve modularity." AI Agent MUST adhere to this defined structure strictly. New files MUST be placed in the appropriate directory based on these descriptions. }

## Component Breakdown & Implementation Details

{ This section outlines the conventions and templates for defining UI components. Detailed specification for most feature-specific components will emerge as user stories are implemented. The AI agent MUST follow the "Template for Component Specification" below whenever a new component is identified for development. }

### Component Naming & Organization

- **Component Naming Convention:** **{e.g., PascalCase for files and component names: `UserProfileCard.tsx`}**. All component files MUST follow this convention.
- **Organization:** {e.g., "Globally reusable components in `src/components/ui/` or `src/components/layout/`. Feature-specific components co-located within their feature directory, e.g., `src/features/feature-name/components/`. Refer to Detailed Frontend Directory Structure.}

### Template for Component Specification

{ For each significant UI component identified from the UI/UX Specification and design files (Figma), the following details MUST be provided. Repeat this subsection for each component. The level of detail MUST be sufficient for an AI agent or developer to implement it with minimal ambiguity. }

#### Component: `{ComponentName}` (e.g., `UserProfileCard`, `ProductDetailsView`)

- **Purpose:** {Briefly describe what this component does and its role in the UI. MUST be clear and concise.}
- **Source File(s):** {e.g., `src/components/user-profile/UserProfileCard.tsx`. MUST be the exact path.}
- **Visual Reference:** {Link to specific Figma frame/component, or Storybook page. REQUIRED.}
- **Props (Properties):**
  { List each prop the component accepts. For each prop, all columns in the table MUST be filled. }
  | Prop Name | Type                                      | Required? | Default Value | Description                                                                                                |
  | :-------------- | :---------------------------------------- | :-------- | :------------ | :--------------------------------------------------------------------------------------------------------- |
  | `userId`        | `string`                                  | Yes       | N/A           | The ID of the user to display. MUST be a valid UUID.                                                     |
  | `avatarUrl`     | `string \| null`                          | No        | `null`        | URL for the user\'s avatar image. MUST be a valid HTTPS URL if provided.                                    |
  | `onEdit`        | `() => void`                              | No        | N/A           | Callback function when an edit action is triggered.                                                        |
  | `variant`       | `\'compact\' \| \'full\'`                     | No        | `\'full\'`        | Controls the display mode of the card.                                                                   |
  | `{anotherProp}` | `{Specific primitive, imported type, or inline interface/type definition}` | {Yes/No}  | {If any}    | {MUST clearly state the prop\'s purpose and any constraints, e.g., \'Must be a positive integer.\'}         |
- **Internal State (if any):**
  { Describe any significant internal state the component manages. Only list state that is *not* derived from props or global state. If state is complex, consider if it should be managed by a custom hook or global state solution instead. }
  | State Variable | Type      | Initial Value | Description                                                                    |
  | :-------------- | :-------- | :------------ | :----------------------------------------------------------------------------- |
  | `isLoading`     | `boolean` | `false`       | Tracks if data for the component is loading.                                   |
  | `{anotherState}`| `{type}`  | `{value}`     | {Description of state variable and its purpose.}                               |
- **Key UI Elements / Structure:**
  { Provide a pseudo-HTML or JSX-like structure representing the component\'s DOM. Include key conditional rendering logic if applicable. **This structure dictates the primary output for the AI agent.** }
  ```html
  <div> <!-- Main card container with specific class e.g., styles.cardFull or styles.cardCompact based on variant prop -->
    <img src="{avatarUrl || defaultAvatar}" alt="User Avatar" class="{styles.avatar}" />
    <h2>{userName}</h2>
    <p class="{variant === 'full' ? styles.emailFull : styles.emailCompact}">{userEmail}</p>
    {variant === 'full' && onEdit && <button onClick={onEdit} class="{styles.editButton}">Edit</button>}
  </div>
  ```
- **Events Handled / Emitted:**
  - **Handles:** {e.g., `onClick` on the edit button (triggers `onEdit` prop).}
  - **Emits:** {If the component emits custom events/callbacks not covered by props, describe them with their exact signature. e.g., `onFollow: (payload: { userId: string; followed: boolean }) => void`}
- **Actions Triggered (Side Effects):**
  - **State Management:** {e.g., "Dispatches `userSlice.actions.setUserName(newName)` from `src/store/slices/userSlice.ts`. Action payload MUST match the defined action creator." OR "Calls `updateUserProfileOptimistic(newData)` from a local `useReducer` hook."}
  - **API Calls:** {Specify which service/function from the "API Interaction Layer" is called. e.g., "Calls `userService.fetchUser(userId)` from `src/services/userService.ts` on mount. Request payload: `{ userId }`. Success response populates internal state `userData`. Error response dispatches `uiSlice.actions.showErrorToast({ message: 'Failed to load user details' })`.}
- **Styling Notes:**
  - {MUST reference specific Design System component names (e.g., "Uses `<Button variant='primary'>` from UI library") OR specify Tailwind CSS classes / CSS module class names to be applied (e.g., "Container uses `p-4 bg-white rounded-lg shadow-md`. Title uses `text-xl font-semibold`.") OR specify SCSS custom component classes to be applied (e.g., "Container uses `@apply p-4 bg-white rounded-lg shadow-md`. Title uses `@apply text-xl font-semibold`."). Any dynamic styling logic based on props or state MUST be described. If Tailwind CSS is used, list primary utility classes or `@apply` directives for custom component classes. AI Agent should prioritize direct utility class usage for simple cases and propose reusable component classes/React components for complex styling patterns.}
- **Accessibility Notes:**
  - {MUST list specific ARIA attributes and their values (e.g., `aria-label="User profile card"`, `role="article"`), required keyboard navigation behavior (e.g., "Tab navigates to avatar, name, email, then edit button. Edit button is focusable and activated by Enter/Space."), and any focus management requirements (e.g., "If this component opens a modal, focus MUST be trapped inside. On modal close, focus returns to the trigger element.").}

---

_Repeat the above template for each significant component._

---

## State Management In-Depth

{ This section expands on the State Management strategy. **Refer to the main Architecture Document for the definitive choice of state management solution.** }

- **Chosen Solution:** {e.g., Redux Toolkit, Zustand, Vuex, NgRx - As defined in main arch doc.}
- **Decision Guide for State Location:**
    - **Global State (e.g., Redux/Zustand):** Data shared across many unrelated components; data persisting across routes; complex state logic managed via reducers/thunks. **MUST be used for session data, user preferences, application-wide notifications.**
    - **React Context API:** State primarily passed down a specific component subtree (e.g., theme, form context). Simpler state, fewer updates compared to global state. **MUST be used for localized state not suitable for prop drilling but not needed globally.**
    - **Local Component State (`useState`, `useReducer`):** UI-specific state, not needed outside the component or its direct children (e.g., form input values, dropdown open/close status). **MUST be the default choice unless criteria for Context or Global State are met.**

### Store Structure / Slices

{ Describe the conventions for organizing the global state (e.g., "Each major feature requiring global state will have its own Redux slice located in `src/features/[featureName]/store.ts`"). }

- **Core Slice Example (e.g., `sessionSlice` in `src/store/slices/sessionSlice.ts`):**
  - **Purpose:** {Manages user session, authentication status, and basic user profile info accessible globally.}
  - **State Shape (Interface/Type):**
    ```typescript
    interface SessionState {
      currentUser: { id: string; name: string; email: string; roles: string[]; } | null;
      isAuthenticated: boolean;
      token: string | null;
      status: "idle" | "loading" | "succeeded" | "failed";
      error: string | null;
    }
    ```
  - **Key Reducers/Actions (within `createSlice`):** {Briefly list main synchronous actions, e.g., `setCurrentUser`, `clearSession`, `setAuthStatus`, `setAuthError`.}
  - **Async Thunks (if any):** {List key async thunks, e.g., `loginUserThunk`, `fetchUserProfileThunk`.}
  - **Selectors (memoized with `createSelector`):** {List key selectors, e.g., `selectCurrentUser`, `selectIsAuthenticated`.}
- **Feature Slice Template (e.g., `{featureName}Slice` in `src/features/{featureName}/store.ts`):**
  - **Purpose:** {To be filled out when a new feature requires its own state slice.}
  - **State Shape (Interface/Type):** {To be defined by the feature.}
  - **Key Reducers/Actions (within `createSlice`):** {To be defined by the feature.}
  - **Async Thunks (if any, defined using `createAsyncThunk`):** {To be defined by the feature.}
  - **Selectors (memoized with `createSelector`):** {To be defined by the feature.}
  - **Export:** {All actions and selectors MUST be exported.}

### Key Selectors

{ List important selectors for any core, upfront slices. For emergent feature slices, selectors will be defined with the slice. **ALL selectors deriving data or combining multiple state pieces MUST use `createSelector` from Reselect (or equivalent for other state libraries) for memoization.** }

- **`selectCurrentUser` (from `sessionSlice`):** {Returns the `currentUser` object.}
- **`selectIsAuthenticated` (from `sessionSlice`):** {Returns `isAuthenticated` boolean.}
- **`selectAuthToken` (from `sessionSlice`):** {Returns the `token` from `sessionSlice`.}

### Key Actions / Reducers / Thunks

{ Detail more complex actions for core, upfront slices, especially asynchronous thunks or sagas. Each thunk MUST clearly define its purpose, parameters, API calls made (referencing the API Interaction Layer), and how it updates the state on pending, fulfilled, and rejected states. }

- **Core Action/Thunk Example: `authenticateUser(credentials: AuthCredentials)` (in `sessionSlice.ts`):**
  - **Purpose:** {Handles user login by calling the auth API and updating the `sessionSlice`.}
  - **Parameters:** `credentials: { email: string; password: string }`
  - **Dispatch Flow (using Redux Toolkit `createAsyncThunk`):**
    1. On `pending`: Dispatches `sessionSlice.actions.setAuthStatus('loading')`.
    2. Calls `authService.login(credentials)` (from `src/services/authService.ts`).
    3. On `fulfilled` (success): Dispatches `sessionSlice.actions.setCurrentUser(response.data.user)`, `sessionSlice.actions.setToken(response.data.token)`, `sessionSlice.actions.setAuthStatus('succeeded')`.
    4. On `rejected` (error): Dispatches `sessionSlice.actions.setAuthError(error.message)`, `sessionSlice.actions.setAuthStatus('failed')`.
- **Feature Action/Thunk Template: `{featureActionName}` (in `{featureName}Slice.ts`):**
  - **Purpose:** {To be filled out for feature-specific async operations.}
  - **Parameters:** {Define specific parameters with types.}
  - **Dispatch Flow (using `createAsyncThunk`):** {To be defined by the feature, following similar patterns for pending, fulfilled, rejected states, including API calls and state updates.}

## API Interaction Layer

{ Describe how the frontend communicates with the backend APIs defined in the main Architecture Document. }

### Client/Service Structure

- **HTTP Client Setup:** {e.g., Axios instance in `src/services/apiClient.ts`. **MUST** include: Base URL (from environment variable `NEXT_PUBLIC_API_URL` or equivalent), default headers (e.g., `Content-Type: 'application/json'`), interceptors for automatic auth token injection (from state management, e.g., `sessionSlice.token`) and standardized error handling/normalization (see below).}
- **Service Definitions (Example):**
  - **`userService.ts` (in `src/services/userService.ts`):**
    - **Purpose:** {Handles all API interactions related to users.}
    - **Functions:** Each service function MUST have explicit parameter types, a return type (e.g., `Promise<User>`), JSDoc/TSDoc explaining purpose, params, return value, and any specific error handling. It MUST call the configured HTTP client (`apiClient`) with correct endpoint, method, and payload.
      - `fetchUser(userId: string): Promise<User>`
      - `updateUserProfile(userId: string, data: UserProfileUpdateDto): Promise<User>`
  - **`productService.ts` (in `src/services/productService.ts`):**
    - **Purpose:** {...}
    - **Functions:** {...}

### Error Handling & Retries (Frontend)

- **Global Error Handling:** {How are API errors caught globally? (e.g., Via Axios response interceptor in `apiClient.ts`). How are they presented/logged? (e.g., Dispatches `uiSlice.actions.showGlobalErrorBanner({ message: error.message })`, logs detailed error to console/monitoring service). Is there a global error state? (e.g., `uiSlice.error`).}
- **Specific Error Handling:** {Components MAY handle specific API errors locally for more contextual feedback (e.g., displaying an inline message on a form field: "Invalid email address"). This MUST be documented in the component's specification if it deviates from global handling.}
- **Retry Logic:** {Is client-side retry logic implemented (e.g., using `axios-retry` with `apiClient`)? If so, specify configuration: max retries (e.g., 3), retry conditions (e.g., network errors, 5xx server errors), retry delay (e.g., exponential backoff). **MUST apply only to idempotent requests (GET, PUT, DELETE).**}

## Routing Strategy

{ Detail how navigation and routing are handled in the frontend application. }

- **Routing Library:** {e.g., React Router, Next.js App Router, Vue Router, Angular Router. As per main Architecture Document.}

### Route Definitions

{ List the main routes of the application and the primary component/page rendered for each. }

| Path Pattern           | Component/Page (`src/app/...` or `src/pages/...`) | Protection                      | Notes                                                 |
| :--------------------- | :-------------------------------------------------- | :------------------------------ | :---------------------------------------------------- |
| `/`                    | `app/page.tsx` or `pages/HomePage.tsx`              | `Public`                        |                                                       |
| `/login`               | `app/login/page.tsx` or `pages/LoginPage.tsx`       | `Public` (redirect if auth)     | Redirects to `/dashboard` if already authenticated.   |
| `/dashboard`           | `app/dashboard/page.tsx` or `pages/DashboardPage.tsx` | `Authenticated`                 |                                                       |
| `/products`            | `app/products/page.tsx`                             | `Public`                        |                                                       |
| `/products/:productId` | `app/products/[productId]/page.tsx`                 | `Public`                        | Parameter: `productId` (string)                       |
| `/settings/profile`    | `app/settings/profile/page.tsx`                     | `Authenticated`, `Role:[USER]`  | Example of role-based protection.                   |
| `{anotherRoute}`       | `{ComponentPath}`                                   | `{Public/Authenticated/Role:[ROLE_NAME]}` | {Notes, parameter names and types}                    |

### Route Guards / Protection

- **Authentication Guard:** {Describe how routes are protected based on authentication status. **Specify the exact HOC, hook, layout, or middleware mechanism and its location** (e.g., `src/guards/AuthGuard.tsx`, or Next.js middleware in `middleware.ts`). Logic MUST use authentication state from the `sessionSlice` (or equivalent). Unauthenticated users attempting to access protected routes MUST be redirected to `/login` (or specified login path).}
- **Authorization Guard (if applicable):** {Describe how routes might be protected based on user roles or permissions. **Specify the exact mechanism**, similar to Auth Guard. Unauthorized users (authenticated but lacking permissions) MUST be shown a "Forbidden" page or redirected to a safe page.}

## Build, Bundling, and Deployment

{ Details specific to the frontend build and deployment process, complementing the "Infrastructure and Deployment Overview" in the main architecture document. }

### Build Process & Scripts

- **Key Build Scripts (from `package.json`):** {e.g., `"build": "next build"`. What do they do? Point to `package.json` scripts. `"dev": "next dev"`, `"start": "next start"`.}. **AI Agent MUST NOT generate code that hardcodes environment-specific values. All such values MUST be accessed via the defined environment configuration mechanism.** Specify the exact files and access method.
- **Environment Configuration Management:** {How are `process.env.NEXT_PUBLIC_API_URL` (or equivalent like `import.meta.env.VITE_API_URL`) managed for different environments (dev, staging, prod)? (e.g., `.env`, `.env.development`, `.env.production` files for Next.js/Vite; build-time injection via CI variables). Specify the exact files and access method.}

### Key Bundling Optimizations

- **Code Splitting:** {How is it implemented/ensured? (e.g., "Next.js/Vite handles route-based code splitting automatically. For component-level code splitting, dynamic imports `React.lazy(() => import('./MyComponent'))` or `import('./heavy-module')` MUST be used for non-critical large components/libraries.")}
- **Tree Shaking:** {How is it implemented/ensured? (e.g., "Ensured by modern build tools like Webpack/Rollup (used by Next.js/Vite) when using ES Modules. Avoid side-effectful imports in shared libraries.")}
- **Lazy Loading (Components, Images, etc.):** {Strategy for lazy loading. (e.g., "Components: `React.lazy` with `Suspense`. Images: Use framework-specific Image component like `next/image` which handles lazy loading by default, or `loading='lazy'` attribute for standard `<img>` tags.")}
- **Minification & Compression:** {Handled by build tools (e.g., Webpack/Terser, Vite/esbuild)? Specify if any specific configuration is needed. Compression (e.g., Gzip, Brotli) is typically handled by the hosting platform/CDN.}

### Deployment to CDN/Hosting

- **Target Platform:** {e.g., Vercel, Netlify, AWS S3/CloudFront, Azure Static Web Apps. As per main Architecture Document.}
- **Deployment Trigger:** {e.g., Git push to `main` branch via GitHub Actions (referencing main CI/CD pipeline).}
- **Asset Caching Strategy:** {How are static assets cached? (e.g., "Immutable assets (JS/CSS bundles with content hashes) have `Cache-Control: public, max-age=31536000, immutable`. HTML files have `Cache-Control: no-cache` or short max-age (e.g., `public, max-age=0, must-revalidate`) to ensure users get fresh entry points. Configured via {hosting platform settings / `next.config.js` headers / CDN rules}.}

## Frontend Testing Strategy

{ This section elaborates on the "Testing Strategy" from the main architecture document, focusing on frontend-specific aspects. **Refer to the main Architecture Document for definitive choices of testing tools.** }

- **Link to Main Overall Testing Strategy:** {Reference the main `docs/architecture.md#overall-testing-strategy` or equivalent.}

### Component Testing

- **Scope:** {Testing individual UI components in isolation (similar to unit tests for components).}
- **Tools:** {e.g., React Testing Library with Jest, Vitest, Vue Test Utils, Angular Testing Utilities. As per main Arch Doc.}
- **Focus:** {Rendering with various props, user interactions (clicks, input changes using `fireEvent` or `userEvent`), event emission, basic internal state changes. **Snapshot testing MUST be used sparingly and with clear justification (e.g., for very stable, purely presentational components with complex DOM structure); prefer explicit assertions.**}
- **Location:** {e.g., `*.test.tsx` or `*.spec.tsx` co-located alongside components, or in a `__tests__` subdirectory.}

### Feature/Flow Testing (UI Integration)

- **Scope:** {Testing how multiple components interact to fulfill a small user flow or feature within a page, potentially mocking API calls or global state management. e.g., testing a complete form submission within a feature, including validation and interaction with a mocked service layer.}
- **Tools:** {Same as component testing (e.g., React Testing Library with Jest/Vitest), but with more complex setups involving mock providers for routing, state, API calls.}
- **Focus:** {Data flow between components, conditional rendering based on interactions, navigation within a feature, integration with mocked services/state.}

### End-to-End UI Testing Tools & Scope

- **Tools:** {Reiterate from main Testing Strategy, e.g., Playwright, Cypress, Selenium.}
- **Scope (Frontend Focus):** {Define 3-5 key user journeys that MUST be covered by E2E UI tests from a UI perspective, e.g., "User registration and login flow", "Adding an item to cart and proceeding to the checkout page summary", "Submitting a complex multi-step form and verifying success UI state and data persistence (via API mocks or a test backend)."}
- **Test Data Management for UI:** {How is consistent test data seeded or mocked for UI E2E tests? (e.g., API mocking layer like MSW, backend seeding scripts, dedicated test accounts).}

## Accessibility (AX) Implementation Details

{ Based on the AX requirements in the UI/UX Specification, detail how these will be technically implemented. }

- **Semantic HTML:** {Emphasis on using correct HTML5 elements. **AI Agent MUST prioritize semantic elements (e.g., `<nav>`, `<button>`, `<article>`) over generic `<div>`/`<span>` with ARIA roles where a native element with the correct semantics exists.**}
- **ARIA Implementation:** {Specify common custom components and their required ARIA patterns (e.g., "Custom select dropdown MUST follow ARIA Combobox pattern including `aria-expanded`, `aria-controls`, `role='combobox'`, etc. Custom Tabs MUST follow ARIA Tabbed Interface pattern."). Link to ARIA Authoring Practices Guide (APG) for reference.}
- **Keyboard Navigation:** {Ensuring all interactive elements are focusable and operable via keyboard. Focus order MUST be logical. Custom components MUST implement keyboard interaction patterns as per ARIA APG (e.g., arrow keys for radio groups/sliders).**}
- **Focus Management:** {How is focus managed in modals, dynamic content changes, route transitions? (e.g., "Modals MUST trap focus. On modal open, focus moves to the first focusable element or the modal container. On close, focus returns to the trigger element. Route changes SHOULD move focus to the main content area or H1 of the new page.")}
- **Testing Tools for AX:** {e.g., Axe DevTools browser extension, Lighthouse accessibility audit. **Automated Axe scans (e.g., using `jest-axe` for component tests, or Playwright/Cypress Axe integration for E2E tests) MUST be integrated into the CI pipeline and fail the build on new violations of WCAG AA (or specified level).** Manual testing procedures: {List key manual checks, e.g., keyboard-only navigation for all interactive elements, screen reader testing (e.g., NVDA/JAWS/VoiceOver) for critical user flows.}}

## Performance Considerations

{ Highlight frontend-specific performance optimization strategies. }

- **Image Optimization:** {Formats (e.g., WebP), responsive images (`<picture>`, `srcset`), lazy loading.}
  - Implementation Mandate: {e.g., "All images MUST use `<Image>` component from Next.js (or equivalent framework-specific optimizer). SVGs for icons. WebP format preferred where supported."}
- **Code Splitting & Lazy Loading (reiterate from Build section if needed):** {How it impacts perceived performance.}
  - Implementation Mandate: {e.g., "Next.js handles route-based code splitting automatically. Dynamic imports `import()` MUST be used for component-level lazy loading."}
- **Minimizing Re-renders:** {Techniques like `React.memo`, `shouldComponentUpdate`, optimized selectors.}
  - Implementation Mandate: {e.g., "`React.memo` MUST be used for components that render frequently with same props. Selectors for global state MUST be memoized (e.g., with Reselect). Avoid passing new object/array literals or inline functions as props directly in render methods where it can cause unnecessary re-renders."}
- **Debouncing/Throttling:** {For event handlers like search input or window resize.}
  - Implementation Mandate: {e.g., "Use a utility like `lodash.debounce` or `lodash.throttle` for specified event handlers. Define debounce/throttle wait times."}
- **Virtualization:** {For long lists or large data sets (e.g., React Virtualized, TanStack Virtual).}
  - Implementation Mandate: {e.g., "MUST be used for any list rendering more than {N, e.g., 100} items if performance degradation is observed."}
- **Caching Strategies (Client-Side):** {Use of browser cache, service workers for PWA capabilities (if applicable).}
  - Implementation Mandate: {e.g., "Configure service worker (if PWA) to cache application shell and key static assets. Leverage HTTP caching headers for other assets as defined in Deployment section."}
- **Performance Monitoring Tools:** {e.g., Lighthouse, WebPageTest, browser DevTools performance tab. Specify which ones are primary and any automated checks in CI.}

## Internationalization (i18n) and Localization (l10n) Strategy

{This section defines the strategy for supporting multiple languages and regional differences if applicable. If not applicable, state "Internationalization is not a requirement for this project at this time."}

- **Requirement Level:** {e.g., Not Required, Required for specific languages [list them], Fully internationalized for future expansion.}
- **Chosen i18n Library/Framework:** {e.g., `react-i18next`, `vue-i18n`, `ngx-translate`, framework-native solution like Next.js i18n routing. Specify the exact library/mechanism.}
- **Translation File Structure & Format:** {e.g., JSON files per language per feature (`src/features/{featureName}/locales/{lang}.json`), or global files (`public/locales/{lang}.json`). Define the exact path and format (e.g., flat JSON, nested JSON).}
- **Translation Key Naming Convention:** {e.g., `featureName.componentName.elementText`, `common.submitButton`. MUST be a clear, consistent, and documented pattern.}
- **Process for Adding New Translatable Strings:** {e.g., "AI Agent MUST add new keys to the default language file (e.g., `en.json`) and use the i18n library's functions/components (e.g., `<Trans>` component, `t()` function) to render text. Keys MUST NOT be constructed dynamically at runtime in a way that prevents static analysis."}
- **Handling Pluralization:** {Specify method/syntax, e.g., using ICU message format via the chosen library (e.g., `t('key', { count: N })`).}
- **Date, Time, and Number Formatting:** {Specify if the i18n library handles this, or if another library (e.g., `date-fns-tz` with locale support, `Intl` API directly) and specific formats/styles should be used for each locale.}
- **Default Language:** {e.g., `en-US`}
- **Language Switching Mechanism (if applicable):** {How is the language changed by the user and persisted? e.g., "Via a language selector component that updates a global state/cookie and potentially alters the URL route."}

## Feature Flag Management

{This section outlines how conditionally enabled features are managed. If not applicable, state "Feature flags are not a primary architectural concern for this project at this time."}

- **Requirement Level:** {e.g., Not Required, Used for specific rollouts, Core part of development workflow.}
- **Chosen Feature Flag System/Library:** {e.g., LaunchDarkly, Unleash, Flagsmith, custom solution using environment variables or a configuration service. Specify the exact tool/method.}
- **Accessing Flags in Code:** {e.g., "Via a custom hook `useFeatureFlag('flag-name'): boolean` or a service `featureFlagService.isOn('flag-name')`. Specify the exact interface, location, and initialization of the service/provider."}
- **Flag Naming Convention:** {e.g., `[SCOPE]_[FEATURE_NAME]_[TARGET_GROUP_OR_TYPE]`, e.g., `CHECKOUT_NEW_PAYMENT_GATEWAY_ROLLOUT`, `USER_PROFILE_BETA_AVATAR_UPLOAD`. MUST be documented and consistently applied.}
- **Code Structure for Flagged Features:** {e.g., "Use conditional rendering (`{isFeatureEnabled && <NewComponent />}`). For larger features, conditionally import components (`React.lazy` with flag check) or routes. Avoid complex branching logic deep within shared components; prefer to flag at higher levels."}
- **Strategy for Code Cleanup (Post-Flag Retirement):** {e.g., "Once a flag is fully rolled out (100% users) and deemed permanent, or fully removed, all conditional logic, old code paths, and the flag itself MUST be removed from the codebase within {N, e.g., 2} sprints. This is a mandatory tech debt item."}
- **Testing Flagged Features:** {How are different flag variations tested? e.g., "QA team uses a debug panel to toggle flags. Automated E2E tests run with specific flag configurations."}

## Frontend Security Considerations

{This section highlights mandatory frontend-specific security practices, complementing the main Architecture Document. AI Agent MUST adhere to these guidelines.}

- **Cross-Site Scripting (XSS) Prevention:**
  - Framework Reliance: {e.g., "React's JSX auto-escaping MUST be relied upon for rendering dynamic content. Vue's `v-html` MUST be avoided unless content is explicitly sanitized."}
  - Explicit Sanitization: {If direct DOM manipulation is unavoidable (strongly discouraged), use {specific sanitization library/function like DOMPurify}. Specify its configuration.}
  - Content Security Policy (CSP): {Is a CSP implemented? How? e.g., "CSP is enforced via HTTP headers set by the backend/CDN as defined in the main Architecture doc. Frontend MAY need to ensure nonce usage for inline scripts if `unsafe-inline` is not allowed." Link to CSP definition if available.}
- **Cross-Site Request Forgery (CSRF) Protection (if applicable for session-based auth):**
  - Mechanism: {e.g., "Backend uses synchronizer token pattern. Frontend ensures tokens are included in state-changing requests if not handled automatically by HTTP client or forms." Refer to main Architecture Document for backend details.}
- **Secure Token Storage & Handling (for client-side tokens like JWTs):**
  - Storage Mechanism: {**MUST specify exact mechanism**: e.g., In-memory via state management (e.g., Redux/Zustand store, cleared on tab close), `HttpOnly` cookies (if backend sets them and frontend doesn't need to read them), `sessionStorage`. **`localStorage` is STRONGLY DISCOURAGED for token storage.**}
  - Token Refresh: {Describe client-side involvement, e.g., "Interceptor in `apiClient.ts` handles 401 errors to trigger token refresh endpoint."}
- **Third-Party Script Security:**
  - Policy: {e.g., "All third-party scripts (analytics, ads, widgets) MUST be vetted for necessity and security. Load scripts asynchronously (`async/defer`)."}
  - Subresource Integrity (SRI): {e.g., "SRI hashes MUST be used for all external scripts and stylesheets loaded from CDNs where the resource is stable."}
- **Client-Side Data Validation:**
  - Purpose: {e.g., "Client-side validation is for UX improvement (immediate feedback) ONLY. **All critical data validation MUST occur server-side** (as defined in the main Architecture Document)."}
  - Implementation: {e.g., "Use {form_library_name like Formik/React Hook Form} for form validation. Rules should mirror server-side validation where appropriate."}
- **Preventing Clickjacking:**
  - Mechanism: {e.g., "Primary defense is `X-Frame-Options` or `frame-ancestors` CSP directive, set by backend/CDN. Frontend code should not rely on frame-busting scripts."}
- **API Key Exposure (for client-side consumed services):**
  - Restriction: {e.g., "API keys for services like Google Maps (client-side JS SDK) MUST be restricted (e.g., HTTP referrer, IP address, API restrictions) via the service provider's console."}
  - Backend Proxy: {e.g., "For keys requiring more secrecy or involving sensitive operations, a backend proxy endpoint MUST be created; frontend calls the proxy, not the third-party service directly."}
- **Secure Communication (HTTPS):**
  - Mandate: {e.g., "All communication with backend APIs MUST use HTTPS. Mixed content (HTTP assets on HTTPS page) is forbidden."}
- **Dependency Vulnerabilities:**
  - Process: {e.g., "Run `npm audit --audit-level=high` (or equivalent) in CI. High/critical vulnerabilities MUST be addressed before deployment. Monitor Dependabot/Snyk alerts."}

## Browser Support and Progressive Enhancement

{This section defines the target browsers and how the application should behave in less capable or non-standard environments.}

- **Target Browsers:** {e.g., "Latest 2 stable versions of Chrome, Firefox, Safari, Edge. Specific versions can be listed if required by project constraints. Internet Explorer (any version) is NOT supported." MUST be explicit.}
- **Polyfill Strategy:**
  - Mechanism: {e.g., "Use `core-js@3` imported at the application entry point. Babel `preset-env` is configured with the above browser targets to include necessary polyfills."}
  - Specific Polyfills (if any beyond `core-js`): {List any other polyfills required for specific features, e.g., `smoothscroll-polyfill`.}
- **JavaScript Requirement & Progressive Enhancement:**
  - Baseline: {e.g., "Core application functionality REQUIRES JavaScript enabled in the browser." OR "Key content (e.g., articles, product information) and primary navigation MUST be accessible and readable without JavaScript. Interactive features and enhancements are layered on top with JavaScript (Progressive Enhancement approach)." Specify the chosen approach.}
  - No-JS Experience (if Progressive Enhancement): {Describe what works without JS. e.g., "Users can view pages and navigate. Forms may not submit or will use standard HTML submission."}
- **CSS Compatibility & Fallbacks:**
  - Tooling: {e.g., "Use Autoprefixer (via PostCSS) configured with the target browser list to add vendor prefixes."}
  - Feature Usage: {e.g., "Avoid CSS features not supported by >90% of target browsers unless a graceful degradation or fallback is explicitly defined and tested (e.g., using `@supports` queries)."}
- **Accessibility Fallbacks:** {Consider how features behave if certain ARIA versions or advanced accessibility features are not supported by older assistive technologies within the support matrix.}

## Change Log

| Change | Date | Version | Description | Author |
| ------ | ---- | ------- | ----------- | ------ |

==================== END: front-end-architecture-tmpl ====================


==================== START: front-end-spec-tmpl ====================
# {Project Name} UI/UX Specification

## Introduction

{State the purpose - to define the user experience goals, information architecture, user flows, and visual design specifications for the project's user interface.}

- **Link to Primary Design Files:** {e.g., Figma, Sketch, Adobe XD URL}
- **Link to Deployed Storybook / Design System:** {URL, if applicable}

## Overall UX Goals & Principles

- **Target User Personas:** {Reference personas or briefly describe key user types and their goals.}
- **Usability Goals:** {e.g., Ease of learning, efficiency of use, error prevention.}
- **Design Principles:** {List 3-5 core principles guiding the UI/UX design - e.g., "Clarity over cleverness", "Consistency", "Provide feedback".}

## Information Architecture (IA)

- **Site Map / Screen Inventory:**
  ```mermaid
  graph TD
      A[Homepage] --> B(Dashboard);
      A --> C{Settings};
      B --> D[View Details];
      C --> E[Profile Settings];
      C --> F[Notification Settings];
  ```
  _(Or provide a list of all screens/pages)_
- **Navigation Structure:** {Describe primary navigation (e.g., top bar, sidebar), secondary navigation, breadcrumbs, etc.}

## User Flows

{Detail key user tasks. Use diagrams or descriptions.}

### {User Flow Name, e.g., User Login}

- **Goal:** {What the user wants to achieve.}
- **Steps / Diagram:**
  ```mermaid
  graph TD
      Start --> EnterCredentials[Enter Email/Password];
      EnterCredentials --> ClickLogin[Click Login Button];
      ClickLogin --> CheckAuth{Auth OK?};
      CheckAuth -- Yes --> Dashboard;
      CheckAuth -- No --> ShowError[Show Error Message];
      ShowError --> EnterCredentials;
  ```
  _(Or: Link to specific flow diagram in Figma/Miro)_

### {Another User Flow Name}

{...}

## Wireframes & Mockups

{Reference the main design file link above. Optionally embed key mockups or describe main screen layouts.}

- **Screen / View Name 1:** {Description of layout and key elements. Link to specific Figma frame/page.}
- **Screen / View Name 2:** {...}

## Component Library / Design System Reference

## Branding & Style Guide Reference

{Link to the primary source or define key elements here.}

- **Color Palette:** {Primary, Secondary, Accent, Feedback colors (hex codes).}
- **Typography:** {Font families, sizes, weights for headings, body, etc.}
- **Iconography:** {Link to icon set, usage notes.}
- **Spacing & Grid:** {Define margins, padding, grid system rules.}

## Accessibility (AX) Requirements

- **Target Compliance:** {e.g., WCAG 2.1 AA}
- **Specific Requirements:** {Keyboard navigation patterns, ARIA landmarks/attributes for complex components, color contrast minimums.}

## Responsiveness

- **Breakpoints:** {Define pixel values for mobile, tablet, desktop, etc.}
- **Adaptation Strategy:** {Describe how layout and components adapt across breakpoints. Reference designs.}

## Change Log

| Change        | Date       | Version | Description         | Author         |
| ------------- | ---------- | ------- | ------------------- | -------------- |

==================== END: front-end-spec-tmpl ====================


==================== START: frontend-service-integration-contract-tmpl ====================
# Frontend Service Integration Contract Template

## Contract Information
- **Contract ID**: [Unique Contract Identifier]
- **Version**: [Contract Version]
- **Status**: [Draft/Active/Deprecated]
- **Created By**: [Author Name and Role]
- **Created Date**: [Creation Date]
- **Last Updated**: [Last Update Date]
- **Review Date**: [Next Review Date]

## Service Overview

### Consumer Service
- **Service Name**: [Frontend Microfrontend Name]
- **Service Type**: [Shell Application/Remote Microfrontend]
- **Technology Stack**: [Next.js, React, etc.]
- **Team Owner**: [Responsible Team]
- **Contact**: [Team Contact Information]

### Provider Service
- **Service Name**: [Backend Service/API Name]
- **Service Type**: [REST API/GraphQL/gRPC/WebSocket]
- **Technology Stack**: [Node.js, .NET, Java, etc.]
- **Team Owner**: [Responsible Team]
- **Contact**: [Team Contact Information]

## Integration Specifications

### Communication Protocol
- **Protocol Type**: [HTTP/HTTPS/WebSocket/gRPC]
- **Base URL**: [Service Base URL]
- **Authentication**: [OAuth 2.0/JWT/API Key/None]
- **Content Type**: [application/json/application/xml/etc.]
- **API Version**: [v1/v2/etc.]

### API Endpoints

#### Endpoint 1: [Endpoint Name]
```http
[METHOD] [PATH]
Content-Type: [content-type]
Authorization: [auth-scheme]

Request Body:
{
  "field1": "type and description",
  "field2": "type and description"
}

Response (200 OK):
{
  "field1": "type and description",
  "field2": "type and description"
}

Response (400 Bad Request):
{
  "error": "string",
  "message": "string",
  "details": []
}
```

#### Endpoint 2: [Endpoint Name]
```http
[METHOD] [PATH]
Content-Type: [content-type]
Authorization: [auth-scheme]

Request Body:
{
  "field1": "type and description",
  "field2": "type and description"
}

Response (200 OK):
{
  "field1": "type and description",
  "field2": "type and description"
}
```

### Data Models

#### Request Models
```typescript
interface [RequestModelName] {
  field1: string; // Description
  field2: number; // Description
  field3?: boolean; // Optional field description
}
```

#### Response Models
```typescript
interface [ResponseModelName] {
  id: string; // Unique identifier
  field1: string; // Description
  field2: number; // Description
  createdAt: string; // ISO 8601 timestamp
  updatedAt: string; // ISO 8601 timestamp
}
```

#### Error Models
```typescript
interface ErrorResponse {
  error: string; // Error code
  message: string; // Human-readable error message
  details?: string[]; // Additional error details
  timestamp: string; // ISO 8601 timestamp
  requestId: string; // Request correlation ID
}
```

## Frontend Integration Patterns

### API Client Implementation
```typescript
// API Client Interface
interface [ServiceName]Client {
  [methodName](request: [RequestType]): Promise<[ResponseType]>;
  [methodName](id: string): Promise<[ResponseType]>;
}

// Implementation Example
class [ServiceName]ClientImpl implements [ServiceName]Client {
  private baseUrl: string;
  private authToken: string;

  constructor(baseUrl: string, authToken: string) {
    this.baseUrl = baseUrl;
    this.authToken = authToken;
  }

  async [methodName](request: [RequestType]): Promise<[ResponseType]> {
    // Implementation details
  }
}
```

### State Management Integration
```typescript
// TanStack Query Integration
export const use[EntityName] = (id: string) => {
  return useQuery({
    queryKey: ['[entity]', id],
    queryFn: () => [serviceName]Client.[methodName](id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Zustand Store Integration
interface [EntityName]Store {
  [entities]: [EntityType][];
  loading: boolean;
  error: string | null;
  fetch[Entities]: () => Promise<void>;
  create[Entity]: (data: [CreateType]) => Promise<void>;
  update[Entity]: (id: string, data: [UpdateType]) => Promise<void>;
  delete[Entity]: (id: string) => Promise<void>;
}
```

### Error Handling Strategy
```typescript
// Error Handling Wrapper
class APIError extends Error {
  constructor(
    public status: number,
    public code: string,
    message: string,
    public details?: string[]
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// Error Handler
const handleAPIError = (error: unknown): APIError => {
  if (error instanceof APIError) {
    return error;
  }
  
  if (error instanceof Response) {
    return new APIError(
      error.status,
      'HTTP_ERROR',
      `HTTP ${error.status}: ${error.statusText}`
    );
  }
  
  return new APIError(500, 'UNKNOWN_ERROR', 'An unknown error occurred');
};
```

## Performance Requirements

### Response Time Targets
| Endpoint | Target Response Time | Maximum Response Time |
|----------|---------------------|----------------------|
| [Endpoint 1] | < 200ms | < 500ms |
| [Endpoint 2] | < 300ms | < 1000ms |
| [Endpoint 3] | < 100ms | < 250ms |

### Throughput Requirements
| Endpoint | Expected RPS | Maximum RPS |
|----------|-------------|-------------|
| [Endpoint 1] | 100 | 500 |
| [Endpoint 2] | 50 | 200 |
| [Endpoint 3] | 200 | 1000 |

### Caching Strategy
```typescript
// Cache Configuration
interface CacheConfig {
  endpoint: string;
  ttl: number; // Time to live in seconds
  strategy: 'cache-first' | 'network-first' | 'stale-while-revalidate';
  invalidationKeys: string[];
}

const cacheConfigs: CacheConfig[] = [
  {
    endpoint: '/api/[endpoint]',
    ttl: 300, // 5 minutes
    strategy: 'stale-while-revalidate',
    invalidationKeys: ['[entity]']
  }
];
```

## Security Requirements

### Authentication & Authorization
```typescript
// Authentication Configuration
interface AuthConfig {
  type: 'oauth2' | 'jwt' | 'api-key';
  tokenEndpoint?: string;
  scope?: string[];
  audience?: string;
}

// Authorization Headers
const getAuthHeaders = (token: string): Record<string, string> => {
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
};
```

### Data Validation
```typescript
// Input Validation Schema
const [requestSchema] = z.object({
  field1: z.string().min(1).max(100),
  field2: z.number().positive(),
  field3: z.boolean().optional()
});

// Validation Function
const validateRequest = (data: unknown): [RequestType] => {
  return [requestSchema].parse(data);
};
```

### Security Headers
```typescript
// Required Security Headers
const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Content-Security-Policy': "default-src 'self'"
};
```

## Testing Strategy

### Contract Testing
```typescript
// Pact Contract Test Example
describe('[Service] Contract Tests', () => {
  const provider = new Pact({
    consumer: '[Consumer Name]',
    provider: '[Provider Name]',
    port: 1234,
    log: path.resolve(process.cwd(), 'logs', 'pact.log'),
    dir: path.resolve(process.cwd(), 'pacts'),
    logLevel: 'INFO'
  });

  beforeAll(() => provider.setup());
  afterAll(() => provider.finalize());

  describe('[Endpoint] interaction', () => {
    beforeEach(() => {
      return provider.addInteraction({
        state: '[Provider State]',
        uponReceiving: '[Interaction Description]',
        withRequest: {
          method: '[METHOD]',
          path: '[PATH]',
          headers: { 'Content-Type': 'application/json' },
          body: { /* request body */ }
        },
        willRespondWith: {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
          body: { /* response body */ }
        }
      });
    });

    it('should [test description]', async () => {
      // Test implementation
    });
  });
});
```

### Integration Testing
```typescript
// Mock Service Worker Setup
const handlers = [
  rest.get('[API_URL]/[endpoint]', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        // Mock response data
      })
    );
  }),
  
  rest.post('[API_URL]/[endpoint]', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        // Mock response data
      })
    );
  })
];

const server = setupServer(...handlers);
```

## Monitoring and Observability

### Metrics Collection
```typescript
// Performance Metrics
interface APIMetrics {
  endpoint: string;
  method: string;
  responseTime: number;
  statusCode: number;
  timestamp: Date;
  userId?: string;
  requestId: string;
}

// Metrics Collection
const collectMetrics = (metrics: APIMetrics) => {
  // Send to monitoring service
  analytics.track('api_call', {
    endpoint: metrics.endpoint,
    method: metrics.method,
    responseTime: metrics.responseTime,
    statusCode: metrics.statusCode,
    timestamp: metrics.timestamp.toISOString()
  });
};
```

### Error Tracking
```typescript
// Error Tracking Configuration
const errorTracker = {
  captureException: (error: Error, context?: Record<string, any>) => {
    // Send to error tracking service (Sentry, etc.)
    console.error('API Error:', error, context);
  },
  
  captureMessage: (message: string, level: 'info' | 'warning' | 'error') => {
    // Send to logging service
    console.log(`[${level.toUpperCase()}] ${message}`);
  }
};
```

## Versioning and Evolution

### API Versioning Strategy
- **Versioning Scheme**: [Semantic Versioning/Date-based/Sequential]
- **Version Header**: [X-API-Version/Accept-Version/URL-based]
- **Backward Compatibility**: [Compatibility guarantee period]
- **Deprecation Policy**: [Deprecation notice period and process]

### Breaking Change Management
```typescript
// Version Compatibility Check
interface VersionCompatibility {
  minVersion: string;
  maxVersion: string;
  deprecatedFeatures: string[];
  newFeatures: string[];
}

const checkCompatibility = (clientVersion: string, serverVersion: string): boolean => {
  // Version compatibility logic
  return true;
};
```

## SLA and Support

### Service Level Agreement
- **Availability**: [99.9%/99.95%/99.99%]
- **Response Time**: [P50/P95/P99 targets]
- **Error Rate**: [Maximum acceptable error rate]
- **Support Hours**: [24/7/Business hours]
- **Escalation Process**: [Support escalation procedure]

### Maintenance Windows
- **Scheduled Maintenance**: [Maintenance schedule and notification process]
- **Emergency Maintenance**: [Emergency maintenance procedures]
- **Rollback Procedures**: [Service rollback and recovery procedures]

## Change Management

### Contract Change Process
1. **Proposal**: [Change proposal and impact assessment]
2. **Review**: [Stakeholder review and approval process]
3. **Implementation**: [Implementation timeline and coordination]
4. **Testing**: [Contract testing and validation]
5. **Deployment**: [Coordinated deployment process]
6. **Monitoring**: [Post-deployment monitoring and validation]

### Communication Plan
- **Stakeholders**: [List of stakeholders to notify]
- **Notification Timeline**: [When to notify about changes]
- **Communication Channels**: [Email/Slack/Documentation updates]
- **Documentation Updates**: [Process for updating documentation]

## Appendices

### A. API Documentation Links
- [OpenAPI/Swagger Documentation URL]
- [Postman Collection URL]
- [GraphQL Schema URL]

### B. Environment Information
| Environment | Base URL | Authentication | Notes |
|-------------|----------|----------------|-------|
| Development | [Dev URL] | [Dev Auth] | [Dev Notes] |
| Staging | [Staging URL] | [Staging Auth] | [Staging Notes] |
| Production | [Prod URL] | [Prod Auth] | [Prod Notes] |

### C. Contact Information
| Role | Name | Email | Slack |
|------|------|-------|-------|
| Frontend Team Lead | [Name] | [Email] | [Slack] |
| Backend Team Lead | [Name] | [Email] | [Slack] |
| Product Owner | [Name] | [Email] | [Slack] |
| DevOps Engineer | [Name] | [Email] | [Slack] |

==================== END: frontend-service-integration-contract-tmpl ====================


==================== START: individual-service-brief-tmpl ====================
# Service Brief: {Service Name}
## Individual Microservice Planning and Design

### Document Information
- **Service Name:** {Service Name}
- **Document Version:** 1.0
- **Creation Date:** {Date}
- **Service Type:** {Core Business/Data/Integration/Platform/AI Agent Service}
- **Team Owner:** {Team Name}
- **Last Updated:** {Date}

---

## 1. Service Overview

### Service Name and Identifier
{Clear, descriptive service identifier following naming conventions.}

### Business Purpose and Value Proposition
{Primary business capability, value delivered, and reason for existence.}

### Service Boundaries and Responsibilities
{Clear responsibility definition, scope limitations, and what the service owns.}

### Team Ownership and Contacts
{Responsible team, primary contacts, support procedures, and escalation paths.}

---

## 2. Business Context

### Domain Alignment and Bounded Context
{Business domain mapping, bounded context definition, and domain model alignment.}

### User Stories and Value Delivery
{Primary user interactions, value delivery scenarios, and business outcomes.}

### Business Rules and Domain Logic
{Domain-specific logic, constraints, validation rules, and business policies.}

### Success Metrics and KPIs
{Service-specific performance indicators, business metrics, and success criteria.}

---

## 3. Technical Context

### Technology Stack and Framework Choices
{Programming language, framework, infrastructure choices, and rationale.}

### Data Requirements and Storage Strategy
{Storage needs, data models, persistence strategy, and database choices.}

### Integration Points and Dependencies
{Upstream and downstream service dependencies, external APIs, and integration requirements.}

### Performance Requirements and Expectations
{Latency targets, throughput expectations, scalability requirements, and SLA commitments.}

---

## 4. Service Architecture

### Component Design and Internal Structure
{Internal service structure, module organization, and architectural patterns.}

### API Design and Interface Specifications
{RESTful endpoints, gRPC interfaces, message schemas, and contract definitions.}

### Data Model and Schema Design
{Entity relationships, data schemas, storage requirements, and data lifecycle.}

### Communication Patterns and Protocols
{Synchronous and asynchronous interaction patterns, messaging, and event handling.}

---

## 5. AI Integration (if applicable)

### AI Capabilities and Integration Points
{Service-specific AI agent integration, workflows, and autonomous capabilities.}

### Model Requirements and Infrastructure
{AI models, vector databases, inference needs, and specialized infrastructure.}

### Human-AI Collaboration Patterns
{Handoff procedures, escalation protocols, and collaboration workflows.}

### AI Infrastructure and Scaling Requirements
{Specialized AI infrastructure, scaling needs, and resource requirements.}

---

## 6. Dependencies and Integration

### Service Dependencies and Requirements
{Required upstream services, external APIs, and dependency management.}

### Downstream Consumers and Clients
{Services and applications that depend on this service, usage patterns.}

### Event Production and Publishing
{Events published by this service, schemas, and triggering conditions.}

### Event Consumption and Subscriptions
{Events consumed from other services, processing logic, and subscription management.}

---

## 7. Quality and Compliance

### Testing Strategy and Approaches
{Unit testing, integration testing, contract testing, and validation strategies.}

### Security Requirements and Controls
{Authentication, authorization, data protection, and security measures.}

### Compliance Needs and Regulatory Requirements
{Regulatory requirements, audit trail management, and compliance validation.}

### Quality Gates and Validation Criteria
{Code quality standards, validation criteria, and acceptance requirements.}

---

## 8. Operational Considerations

### Deployment Strategy and Approach
{CI/CD pipelines, containerization, orchestration, and deployment procedures.}

### Monitoring Requirements and Health Checks
{Health endpoints, metrics collection, alerting, and observability needs.}

### Scaling Strategy and Resource Management
{Auto-scaling policies, resource requirements, and capacity planning.}

### Maintenance Procedures and Operations
{Updates, patches, operational tasks, and maintenance windows.}

---

## 9. Implementation Planning

### Development Timeline and Milestones
{Estimated effort, delivery milestones, and development phases.}

### Resource Requirements and Team Needs
{Team size, skill requirements, infrastructure needs, and resource allocation.}

### Risk Assessment and Mitigation
{Technical risks, integration risks, operational risks, and mitigation strategies.}

### Dependencies and Critical Path
{Critical dependencies, blocking factors, and coordination requirements.}

---

## 10. Service Specifications

### Functional Requirements Summary
{Core functionality, business logic, and service capabilities.}

### Non-Functional Requirements
{Performance, scalability, reliability, and operational requirements.}

### API Contract Overview
{High-level API design, endpoint categories, and interaction patterns.}

### Data Ownership and Management
{Data ownership boundaries, lifecycle management, and governance.}

---

## 11. Integration and Communication

### Synchronous Communication
{RESTful APIs, gRPC interfaces, and direct service-to-service communication.}

### Asynchronous Communication
{Event publishing, message queues, and asynchronous processing patterns.}

### External Integrations
{Third-party APIs, legacy systems, and external service dependencies.}

### Service Discovery and Registration
{Service registration, discovery mechanisms, and load balancing.}

---

## 12. Security and Governance

### Authentication and Authorization
{Service-to-service authentication, user authentication, and access control.}

### Data Protection and Privacy
{Encryption, data handling, privacy requirements, and protection measures.}

### Audit and Compliance
{Audit trail requirements, compliance validation, and regulatory adherence.}

### Security Monitoring and Incident Response
{Security monitoring, threat detection, and incident response procedures.}

---

## 13. Performance and Scalability

### Performance Targets and SLAs
{Response time targets, throughput requirements, and service level agreements.}

### Scalability Strategy
{Horizontal scaling, vertical scaling, and auto-scaling configurations.}

### Resource Optimization
{CPU, memory, storage optimization, and resource efficiency.}

### Capacity Planning
{Growth projections, resource allocation, and capacity management.}

---

## 14. Handoff Instructions

### Service PRD Development Prompt
This Service Brief provides comprehensive context for {Service Name}. Please proceed to create a detailed Service PRD that addresses:

1. **Detailed functional and non-functional requirements**
2. **Comprehensive API specifications and contracts**
3. **Data model definitions and storage requirements**
4. **Integration patterns and communication protocols**
5. **Testing strategies and quality assurance procedures**
6. **Deployment and operational specifications**

Start in 'Service PRD Generation Mode', review this brief thoroughly, and work with the user to create detailed service specifications section by section.

### Architecture Review and Validation
{Instructions for technical design validation and optimization.}

### Integration Planning and Coordination
{Cross-service coordination requirements and dependency management.}

### Implementation Guidance
{Development team instructions, best practices, and implementation guidelines.}

---

## 15. Change Log

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 1.0 | {Date} | Initial creation | {Author} |

---

## 16. Appendices

### Glossary
{Service-specific terms and technical concepts.}

### Reference Documents
{Related documents, specifications, and external references.}

### Stakeholder Information
{Service stakeholders, contacts, and communication channels.}

==================== END: individual-service-brief-tmpl ====================


==================== START: individual-service-prd-tmpl ====================
# Service PRD: {Service Name}
## Detailed Requirements and Technical Specifications

### Document Information
- **Service Name:** {Service Name}
- **Document Version:** 1.0
- **Creation Date:** {Date}
- **Service Type:** {Core Business/Data/Integration/Platform/AI Agent Service}
- **Owner Team:** {Team Name and Contacts}
- **Business Domain:** {Domain and Bounded Context}
- **Last Updated:** {Date and Change Summary}

---

## 1. Service Definition and Context

### Service Mission and Purpose
{Core business capability, value proposition, and reason for existence.}

### Business Domain and Bounded Context
{Domain alignment, bounded context definition, and business capability mapping.}

### Service Boundaries and Responsibilities
{Clear definition of what the service owns, manages, and is responsible for.}

### Stakeholders and Users
{Primary users, stakeholders, and consumers of the service.}

---

## 2. Business Requirements

### Primary Purpose and Value Delivery
{Core business capability, value proposition, and business outcomes.}

### User Stories and Acceptance Criteria
{Detailed user interactions, scenarios, and acceptance criteria.}

### Business Rules and Domain Logic
{Domain-specific logic, constraints, validations, and business policies.}

### Success Criteria and Metrics
{Measurable outcomes, performance indicators, and success definitions.}

---

## 3. Functional Requirements

### Core Capabilities and Features
{Primary service functions, features, and business logic capabilities.}

### API Specifications and Endpoints
{RESTful endpoints with detailed request/response schemas and examples.}

### Data Operations and Processing
{CRUD operations, queries, data transformations, and processing logic.}

### Business Logic and Algorithms
{Algorithms, calculations, decision-making processes, and business workflows.}

---

## 4. Non-Functional Requirements

### Performance Requirements
{Latency targets, throughput requirements, and response time specifications.}

### Scalability Requirements
{Horizontal scaling needs, load handling capacity, and resource utilization targets.}

### Reliability and Availability
{Uptime requirements, fault tolerance, error recovery, and resilience specifications.}

### Security Requirements
{Authentication, authorization, encryption, data protection, and security controls.}

---

## 5. API Design and Contracts

### RESTful Endpoint Specifications
{Complete API specification with request/response schemas, examples, and documentation.}

#### Core Endpoints
- **GET /api/v1/{resource}** - {Description and usage}
- **POST /api/v1/{resource}** - {Description and usage}
- **PUT /api/v1/{resource}/{id}** - {Description and usage}
- **DELETE /api/v1/{resource}/{id}** - {Description and usage}

### Request/Response Schemas
{JSON schemas, validation rules, and data format specifications.}

### Error Handling and Response Codes
{Error codes, messages, recovery procedures, and error response formats.}

### API Versioning Strategy
{API evolution approach, backward compatibility, and versioning policies.}

---

## 6. Data Model and Storage

### Entity Definitions and Relationships
{Data models, entity relationships, constraints, and business rules.}

### Database Schema and Structure
{Table structures, indexes, constraints, and optimization strategies.}

### Data Validation and Integrity
{Input validation, business rules, integrity checks, and data quality measures.}

### Data Lifecycle Management
{Creation, updates, archival, deletion policies, and data retention.}

---

## 7. Integration Specifications

### Service Dependencies and Requirements
{Required external services, APIs, and dependency management.}

### Event Production and Publishing
{Events published with schemas, triggers, and publishing patterns.}

### Event Consumption and Processing
{Subscribed events, processing logic, and consumption patterns.}

### External APIs and Third-Party Integrations
{Third-party integrations, external data sources, and API dependencies.}

---

## 8. AI Integration (if applicable)

### AI Agent Capabilities and Workflows
{Service-specific AI functionality, workflows, and autonomous capabilities.}

### Model Integration and Inference
{AI models, inference endpoints, scaling, and model management.}

### Vector Database and Semantic Search
{Embedding storage, semantic search, retrieval, and vector operations.}

### Human-AI Handoff Procedures
{Escalation procedures, collaboration patterns, and handoff protocols.}

---

## 9. Security and Compliance

### Authentication and Authorization
{Service-to-service authentication, user authentication, and access control mechanisms.}

### Authorization and Permission Management
{Role-based access control, permission management, and authorization policies.}

### Data Protection and Encryption
{Encryption standards, privacy measures, and data handling procedures.}

### Compliance and Regulatory Requirements
{Regulatory requirements, audit trail management, and compliance validation.}

---

## 10. Testing and Quality Assurance

### Unit Testing Strategy
{Component testing approach, coverage requirements, and testing frameworks.}

### Integration Testing Approach
{Service interaction validation, contract testing, and integration scenarios.}

### Performance Testing Requirements
{Load testing, stress testing, performance validation, and optimization.}

### Security Testing and Validation
{Vulnerability assessment, penetration testing, and security validation.}

---

## 11. Deployment and Operations

### Containerization and Packaging
{Docker configuration, image management, and container specifications.}

### Orchestration and Kubernetes Configuration
{Kubernetes deployment, service configuration, and resource management.}

### CI/CD Pipeline Requirements
{Build processes, testing automation, deployment automation, and pipeline configuration.}

### Environment Configuration Management
{Development, staging, production settings, and configuration management.}

---

## 12. Monitoring and Observability

### Health Checks and Service Monitoring
{Health endpoints, service validation, and monitoring requirements.}

### Metrics Collection and KPIs
{Performance metrics, business metrics, KPIs, and measurement strategies.}

### Logging Strategy and Requirements
{Structured logging, log levels, retention policies, and log management.}

### Alerting Rules and Escalation
{Threshold-based alerts, escalation procedures, and incident response.}

---

## 13. Scaling and Performance

### Auto-Scaling Configuration
{Horizontal scaling policies, triggers, and scaling parameters.}

### Resource Management and Optimization
{CPU, memory, storage requirements, and resource optimization strategies.}

### Performance Optimization Strategies
{Caching strategies, indexing, query optimization, and performance tuning.}

### Capacity Planning and Growth
{Growth projections, resource allocation, and capacity management.}

---

## 14. Disaster Recovery and Business Continuity

### Backup Strategy and Procedures
{Data backup, retention policies, and recovery procedures.}

### Failover and Redundancy
{Service redundancy, automatic failover, and high availability configuration.}

### Recovery Time and Point Objectives
{RTO and RPO requirements, recovery procedures, and business continuity.}

### Business Continuity Planning
{Critical function preservation, restoration procedures, and continuity measures.}

---

## 15. Documentation and Knowledge Management

### API Documentation and Examples
{Comprehensive endpoint documentation, examples, and usage guides.}

### Operational Runbooks
{Operational procedures, troubleshooting guides, and maintenance documentation.}

### Architecture Documentation
{Service design, technical specifications, and architectural decisions.}

### Team Knowledge and Onboarding
{Onboarding materials, knowledge sharing, and team documentation.}

---

## 16. Change Management and Evolution

### Version Control and Configuration
{Code versioning, configuration management, and change tracking.}

### Change Approval and Review Process
{Review procedures, stakeholder approval, and change management workflow.}

### Release Management and Deployment
{Deployment coordination, rollback procedures, and release management.}

### Impact Assessment and Risk Evaluation
{Change impact analysis, risk evaluation, and mitigation strategies.}

---

## 17. Implementation Timeline and Phases

### Development Phases and Milestones
{Incremental delivery phases, milestone planning, and development timeline.}

### Dependencies and Critical Path
{Critical path analysis, coordination requirements, and dependency management.}

### Resource Allocation and Team Assignment
{Team assignments, skill requirements, and resource planning.}

### Risk Management and Mitigation
{Risk identification, assessment, mitigation strategies, and contingency planning.}

---

## 18. Acceptance Criteria and Definition of Done

### Functional Acceptance Criteria
{Feature completeness, business requirement validation, and functional testing.}

### Technical Acceptance Criteria
{Code quality, performance validation, security validation, and technical standards.}

### Operational Acceptance Criteria
{Deployment readiness, monitoring setup, and maintenance procedures.}

### Documentation Acceptance Criteria
{Complete documentation, knowledge transfer, and documentation standards.}

---

## 19. Post-Implementation Support

### Maintenance Procedures and Updates
{Regular updates, patches, improvements, and maintenance schedules.}

### Support Escalation and Issue Resolution
{Issue resolution procedures, expert consultation, and support escalation.}

### Performance Monitoring and Optimization
{Ongoing optimization, capacity management, and performance monitoring.}

### Evolution Planning and Enhancement
{Future enhancements, technology migration, and evolution planning.}

---

## 20. Handoff Instructions

### Development Team Prompt
{Implementation guidance, technical specifications, and development instructions.}

### DevOps Team Prompt
{Deployment configuration, operational setup, and infrastructure requirements.}

### QA Team Prompt
{Testing strategy, validation procedures, and quality assurance requirements.}

### Documentation Team Prompt
{Knowledge management, user documentation, and documentation requirements.}

---

## 21. Change Log

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 1.0 | {Date} | Initial creation | {Author} |

---

## 22. Appendices

### Glossary
{Service-specific terms and technical concepts.}

### Reference Documents
{Supporting documents and external references.}

### Technical Specifications
{Detailed technical specifications and configurations.}

==================== END: individual-service-prd-tmpl ====================


==================== START: master-project-brief-tmpl ====================
# Master Project Brief: {Project Name}
## Microservices Architecture with Agentic AI Integration

### Document Information
- **Project Name:** {Project Name}
- **Document Version:** 1.0
- **Creation Date:** {Date}
- **Project Type:** Microservices Architecture with Agentic AI Capabilities
- **Analyst:** {Analyst Name}
- **Last Updated:** {Date}

---

## 1. Executive Summary and Vision

### Project Overview
{Provide a high-level description of the project, its strategic context, and the problem it solves. Explain why a microservices architecture with agentic AI capabilities is the chosen approach.}

### Business Value Proposition
{Describe the ROI, competitive advantage, and market opportunity. Include quantifiable benefits and strategic impact.}

### Success Metrics and KPIs
{Define measurable outcomes, OKRs, and key performance indicators for the project.}
- **Primary Success Metrics:**
  - Metric 1: {Description and target}
  - Metric 2: {Description and target}
  - Metric 3: {Description and target}

### Timeline and Major Milestones
{Outline major phases, delivery targets, and key decision points.}

---

## 2. System Architecture Overview

### Microservices Topology
{Describe the high-level service map, communication patterns, and system boundaries.}

### Technology Stack Decisions
{Document programming languages, frameworks, infrastructure choices, and rationale.}
- **Frontend Technologies:** {Next.js 14+, Module Federation, etc.}
- **Backend Technologies:** {Python/FastAPI, Java/Spring Boot, Go, etc.}
- **Infrastructure:** {Kubernetes, Service Mesh, Cloud Platform}
- **AI/ML Stack:** {LangChain/LangGraph, Vector Databases, Model Serving}

### Integration Architecture
{Describe external systems, APIs, third-party services, and legacy system integration.}

### Data Architecture Strategy
{Outline polyglot persistence approach, data flow patterns, and consistency models.}

---

## 3. Service Decomposition Strategy

### Business Capability Mapping
{Map business domains to potential microservices using domain-driven design principles.}

### Service Boundary Analysis
{Define bounded contexts, service responsibilities, and clear ownership boundaries.}

### Service Catalog Preview
{Provide an overview of planned microservices and their primary purposes.}
- **Core Business Services:** {List 3-5 primary business logic services}
- **Data Services:** {List data management and analytics services}
- **Integration Services:** {List external connectivity services}
- **Platform Services:** {List infrastructure and operational services}
- **AI Agent Services:** {List agentic AI capabilities}

### Evolution Strategy
{Plan for service splitting, merging, boundary adjustments, and growth.}

---

## 4. AI Integration Vision

### Agentic AI Capabilities
{Describe the AI agent roles, responsibilities, and autonomous capabilities planned for the system.}

### Human-AI Collaboration Framework
{Define handoff procedures, escalation protocols, and collaboration patterns.}
- **Human-in-the-Loop Scenarios:** {Critical decisions requiring human judgment}
- **Human-on-the-Loop Scenarios:** {AI autonomous with human monitoring}
- **Human-out-of-the-Loop Scenarios:** {Fully autonomous operations}

### AI Infrastructure Requirements
{Outline vector databases, model serving, scaling needs, and AI-specific infrastructure.}

### AI Governance Framework
{Address AI ethics, compliance, quality assurance, and risk management.}

---

## 5. Platform Requirements

### Infrastructure Needs
{Define Kubernetes requirements, service mesh needs, and cloud service dependencies.}

### Developer Experience Vision
{Describe tooling, automation, self-service capabilities, and productivity goals.}

### Internal Developer Platform (IDP)
{Outline golden paths, platform-as-a-product approach, and self-service capabilities.}

### Operational Excellence
{Define monitoring, observability, incident management, and performance requirements.}

---

## 6. Team Topology and Organization

### Stream-Aligned Teams
{Define service ownership, team boundaries, and responsibility areas.}

### Platform Teams
{Describe infrastructure teams, developer experience teams, and platform responsibilities.}

### Enabling Teams
{Identify cross-cutting expertise needs and knowledge sharing requirements.}

### Conway's Law Optimization
{Align organizational design with desired architecture and communication patterns.}

---

## 7. Implementation Strategy

### Phased Delivery Plan
{Outline incremental value delivery approach and risk management strategy.}
- **Phase 1: Foundation (Months 1-3):** {Platform setup and core infrastructure}
- **Phase 2: Core Services (Months 4-8):** {Business logic and data services}
- **Phase 3: AI Integration (Months 6-10):** {Agentic AI capabilities and orchestration}
- **Phase 4: Advanced Features (Months 9-12):** {Advanced capabilities and optimization}

### Technology Migration Strategy
{Plan legacy system integration, modernization approach, and migration timeline.}

### Risk Assessment and Mitigation
{Identify technical, organizational, and business risks with mitigation strategies.}

### Change Management
{Address training, adoption, cultural transformation, and organizational change.}

---

## 8. Governance and Compliance

### Security Framework
{Define Zero Trust architecture, encryption standards, and access control requirements.}

### Compliance Requirements
{Address regulatory, industry, and organizational standards and requirements.}

### Quality Assurance Standards
{Define testing strategies, code quality standards, and documentation requirements.}

### Financial Operations (FinOps)
{Plan cost management, optimization strategies, and financial governance.}

---

## 9. Known Technical Constraints and Preferences

### Technical Constraints
{List any known limitations, mandatory technologies, or compliance requirements.}

### Initial Architectural Preferences
{Capture early thoughts on repository structure, service architecture, and technology choices.}
- **Repository Strategy:** {Monorepo vs. Polyrepo preference and rationale}
- **Service Architecture:** {Microservices granularity and communication preferences}
- **Technology Mandates:** {Required technologies, platforms, or frameworks}

### Integration Requirements
{Define required integrations with existing systems, APIs, or third-party services.}

### Performance and Scalability Requirements
{Outline expected load, performance targets, and scalability needs.}

---

## 10. Relevant Research and Context

### Market Analysis
{Summarize relevant market research, competitive analysis, or industry trends.}

### Technical Research
{Reference any technical research, proof-of-concepts, or feasibility studies.}

### Stakeholder Input
{Summarize key stakeholder feedback, requirements, or constraints.}

---

## 11. Next Steps and Handoff Instructions

### Product Manager Prompt
This Master Project Brief provides comprehensive context for {Project Name}. Please proceed to create a Master Project PRD that addresses:

1. **System-wide requirements and architecture specifications**
2. **Cross-cutting concerns and enterprise-level capabilities**
3. **Service catalog management and dependency coordination**
4. **AI agent ecosystem design and orchestration planning**
5. **Platform engineering requirements and developer experience**

Start in 'Master PRD Generation Mode', review this brief thoroughly, and work with the user to create comprehensive system-level requirements section by section.

### Key Stakeholders and Decision Makers
{List primary stakeholders, decision makers, approvers, and communication channels.}

### Success Criteria for Project Brief Phase
{Define what constitutes completion of the project brief phase and readiness for PRD development.}

### Reference Materials and Supporting Documents
{List supporting documents, research reports, technical specifications, or external resources.}

---

## 12. Appendices

### Glossary of Terms
{Define key terms, acronyms, and technical concepts used in the project.}

### Assumptions and Dependencies
{List key assumptions made and external dependencies identified.}

### Change Log
| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 1.0 | {Date} | Initial creation | {Author} |

==================== END: master-project-brief-tmpl ====================


==================== START: master-project-prd-tmpl ====================
# Master Project PRD: {Project Name}
## System-Wide Requirements and Architecture Specifications

### Document Information
- **Project Name:** {Project Name}
- **Document Version:** 1.0
- **Creation Date:** {Date}
- **Document Type:** Master Project Product Requirements Document
- **Product Manager:** {PM Name}
- **Last Updated:** {Date}

---

## 1. Project Context and Objectives

### Business Goals and Strategic Objectives
{Define the strategic objectives, value proposition, and business impact of the system.}

### User Personas and Stakeholders
{Define primary and secondary user types across all services, including internal and external stakeholders.}

### Success Metrics and Measurement Strategy
{Define system-wide KPIs, measurement strategies, and success criteria.}

### Constraints and Assumptions
{Document technical, business, organizational limitations, and key assumptions.}

---

## 2. System Architecture and Design

### High-Level Architecture Overview
{Describe the system topology, major components, and architectural patterns.}

### Service Catalog and Inventory
{Provide complete inventory of planned microservices with descriptions and responsibilities.}

#### Core Business Services
- **Service 1:** {Name and primary business capability}
- **Service 2:** {Name and primary business capability}
- **Service N:** {Name and primary business capability}

#### Data Services
- **Analytics Service:** {Data processing and insights}
- **Intelligence Hub:** {Centralized analytics and predictive capabilities}
- **Data Management Service:** {Data governance and lifecycle management}

#### Integration Services
- **API Gateway Service:** {External API management and routing}
- **Legacy Integration Service:** {Legacy system connectivity}
- **Third-Party Integration Service:** {External service integrations}

#### Platform Services
- **Authentication Service:** {Identity and access management}
- **Configuration Service:** {Centralized configuration management}
- **Monitoring Service:** {System observability and health}

#### AI Agent Services
- **Agent Orchestration Service:** {Multi-agent workflow coordination}
- **Conversational AI Service:** {Natural language processing}
- **Automation Engine Service:** {Task automation and execution}

### Communication Patterns and Integration
{Define inter-service protocols, messaging strategies, and integration patterns.}

### Data Architecture and Flow Design
{Describe polyglot persistence strategy, data flow patterns, and consistency models.}

---

## 3. Cross-Cutting Requirements

### Security and Compliance Framework
{Define system-wide security requirements and regulatory compliance needs.}

### Performance and Scalability Requirements
{Specify system-level SLAs, scaling strategies, and performance targets.}

### Reliability and Resilience Requirements
{Define fault tolerance, disaster recovery, and business continuity requirements.}

### Observability and Monitoring Requirements
{Specify centralized logging, metrics, alerting, and observability needs.}

---

## 4. AI Agent Ecosystem

### Agent Orchestration Service
{Define multi-agent workflow coordination, task distribution, and orchestration capabilities.}

### Intelligence Hub Service
{Specify centralized analytics, insights, predictive capabilities, and data intelligence.}

### Conversational AI Service
{Define natural language understanding, generation, and conversational capabilities.}

### Automation Engine Service
{Specify task automation, decision execution, and workflow automation capabilities.}

### Learning & Adaptation Service
{Define continuous improvement, model evolution, and adaptive learning capabilities.}

---

## 5. Platform Engineering Strategy

### Internal Developer Platform (IDP) Requirements
{Define self-service capabilities, golden paths, and platform-as-a-product approach.}

### Infrastructure Requirements
{Specify Kubernetes, service mesh, cloud services, and infrastructure needs.}

### Developer Experience Optimization
{Define tooling, automation, productivity optimization, and developer workflows.}

### Platform Team Responsibilities
{Specify platform team structure, responsibilities, and service catalog management.}

---

## 6. Epic Overview and Service Breakdown

### Epic 1: Platform Foundation
**Goal:** Establish infrastructure, security, and core platform services
- **Story 1.1:** Platform Infrastructure Setup
- **Story 1.2:** Security and Identity Management
- **Story 1.3:** Monitoring and Observability Foundation
- **Story 1.4:** CI/CD Pipeline Implementation

### Epic 2: Core Business Services
**Goal:** Implement primary business logic and workflows
- **Story 2.1:** {Primary Business Service Implementation}
- **Story 2.2:** {Secondary Business Service Implementation}
- **Story 2.3:** {Business Workflow Integration}

### Epic 3: Data Services
**Goal:** Implement analytics, intelligence, and data management capabilities
- **Story 3.1:** Data Pipeline Implementation
- **Story 3.2:** Analytics and Reporting Service
- **Story 3.3:** Intelligence Hub Development

### Epic 4: Integration Services
**Goal:** Connect external APIs and legacy systems
- **Story 4.1:** API Gateway Implementation
- **Story 4.2:** Legacy System Integration
- **Story 4.3:** Third-Party Service Integration

### Epic 5: AI Agent Services
**Goal:** Implement agentic AI capabilities and orchestration
- **Story 5.1:** Agent Orchestration Service
- **Story 5.2:** Conversational AI Implementation
- **Story 5.3:** Automation Engine Development

### Epic 6: Frontend Applications
**Goal:** Implement user interfaces and micro-frontend architecture
- **Story 6.1:** Shell Application Development
- **Story 6.2:** Micro-Frontend Implementation
- **Story 6.3:** User Experience Integration

---

## 7. Service Dependencies and Integration

### Service Dependency Matrix
{Define cross-service relationships, communication patterns, and dependencies.}

### API Contract Specifications
{Specify RESTful endpoints, gRPC interfaces, and message schemas.}

### Event-Driven Architecture
{Define event schemas, topics, subscription patterns, and event flow.}

### External Integrations
{Specify third-party APIs, legacy systems, and cloud service integrations.}

---

## 8. Implementation Timeline and Phases

### Phase 1: Foundation (Months 1-3)
{Platform setup, core infrastructure, and foundational services.}

### Phase 2: Core Services (Months 4-8)
{Business logic implementation and data services development.}

### Phase 3: AI Integration (Months 6-10)
{Agentic AI capabilities implementation and orchestration.}

### Phase 4: Advanced Features (Months 9-12)
{Advanced capabilities, optimization, and full system integration.}

---

## 9. Quality Assurance and Testing

### Testing Strategy
{Define unit, integration, contract, and end-to-end testing approaches.}

### Quality Gates and Standards
{Specify code quality, security scanning, and performance validation requirements.}

### Compliance Validation
{Define regulatory requirements validation and audit trail management.}

### Continuous Improvement
{Specify feedback loops and iterative enhancement processes.}

---

## 10. Operational Excellence

### Monitoring and Alerting
{Define system health monitoring, performance metrics, and incident detection.}

### Incident Management
{Specify response procedures, escalation protocols, and post-mortem analysis.}

### Capacity Planning
{Define resource allocation, scaling strategies, and cost optimization.}

### Disaster Recovery
{Specify backup strategies, failover procedures, and business continuity.}

---

## 11. Change Management and Evolution

### Version Control and Documentation
{Define document versioning, change tracking, and documentation standards.}

### Approval Workflow
{Specify stakeholder review processes and decision-making procedures.}

### Evolution Strategy
{Plan system growth, service evolution, and technology migration.}

### Knowledge Management
{Define documentation standards, training, and knowledge sharing.}

---

## 12. Handoff Instructions

### Design Architect Prompt
{Instructions for frontend architecture and micro-frontend design.}

### Platform Architect Prompt
{Instructions for infrastructure design and platform engineering.}

### AI Orchestration Agent Prompt
{Instructions for AI agent coordination and workflow design.}

### Individual Service PRD Generation
{Instructions for service-specific requirements and specifications.}

---

## 13. Technical Assumptions and Decisions

### Repository and Service Architecture
{Document chosen repository structure and service architecture with rationale.}

### Technology Stack Selections
{Specify core technologies, frameworks, and infrastructure choices.}

### Integration and Communication Patterns
{Define service communication protocols and integration strategies.}

---

## 14. Change Log

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 1.0 | {Date} | Initial creation | {Author} |

---

## 15. Appendices

### Glossary
{Define key terms and technical concepts.}

### Reference Documents
{List supporting documents and external references.}

### Stakeholder Matrix
{Define roles, responsibilities, and communication channels.}

==================== END: master-project-prd-tmpl ====================


==================== START: microfrontend-architecture-tmpl ====================
# Microfrontend Architecture Template

## Document Information
- **Document Type**: Microfrontend Architecture Specification
- **Version**: 1.0
- **Status**: [Draft/Review/Approved]
- **Author**: [Microfrontend Architect Name]
- **Date**: [Creation Date]
- **Last Updated**: [Last Update Date]

## Executive Summary

### Business Context
- **Project Name**: [Project Name]
- **Business Domain**: [Domain Description]
- **Strategic Objectives**: [Key business goals]
- **Success Criteria**: [Measurable success metrics]

### Architecture Overview
- **Architecture Pattern**: [Shell Application/Federated/Hybrid]
- **Technology Stack**: [Primary technologies and frameworks]
- **Team Structure**: [Number of teams and ownership model]
- **Deployment Strategy**: [Independent/Coordinated deployment approach]

## Microfrontend Decomposition Strategy

### Domain Boundaries
```
Domain 1: [Domain Name]
├── Business Capabilities: [List of capabilities]
├── User Journeys: [Primary user flows]
├── Data Ownership: [Data entities owned]
└── Team Ownership: [Responsible team]

Domain 2: [Domain Name]
├── Business Capabilities: [List of capabilities]
├── User Journeys: [Primary user flows]
├── Data Ownership: [Data entities owned]
└── Team Ownership: [Responsible team]
```

### Microfrontend Inventory
| Microfrontend | Domain | Technology | Team | Deployment |
|---------------|--------|------------|------|------------|
| [MF Name] | [Domain] | [Tech Stack] | [Team] | [Strategy] |
| [MF Name] | [Domain] | [Tech Stack] | [Team] | [Strategy] |

### Boundary Definitions
- **Functional Boundaries**: [How features are divided]
- **Data Boundaries**: [Data ownership and sharing rules]
- **UI Boundaries**: [Visual and interaction boundaries]
- **Team Boundaries**: [Organizational ownership model]

## Technical Architecture

### Shell Application Design
```
Shell Application (Host)
├── Global Navigation
├── Authentication & Authorization
├── Theme & Design System Provider
├── Error Boundary Management
├── Performance Monitoring
└── Microfrontend Orchestration
```

#### Shell Responsibilities
- **Routing Coordination**: [Top-level routing strategy]
- **Authentication Management**: [Auth flow and token management]
- **Global State**: [Shared state management approach]
- **Error Handling**: [Global error boundary strategy]
- **Performance Monitoring**: [Monitoring and analytics integration]

### Module Federation Configuration

#### Host Configuration
```javascript
// webpack.config.js - Host
module.exports = {
  mode: 'development',
  plugins: [
    new ModuleFederationPlugin({
      name: '[host-name]',
      remotes: {
        '[mf-name]': '[mf-name]@[url]/remoteEntry.js',
      },
      shared: {
        // Shared dependencies configuration
      },
    }),
  ],
};
```

#### Remote Configuration Template
```javascript
// webpack.config.js - Remote
module.exports = {
  mode: 'development',
  plugins: [
    new ModuleFederationPlugin({
      name: '[remote-name]',
      filename: 'remoteEntry.js',
      exposes: {
        './[Component]': './src/[ComponentPath]',
      },
      shared: {
        // Shared dependencies configuration
      },
    }),
  ],
};
```

### Communication Architecture

#### Event-Driven Communication
```typescript
// Event Bus Interface
interface EventBus {
  publish(event: string, data: any): void;
  subscribe(event: string, handler: Function): void;
  unsubscribe(event: string, handler: Function): void;
}

// Event Types
interface DomainEvents {
  'user.authenticated': UserAuthenticatedEvent;
  'navigation.changed': NavigationChangedEvent;
  'data.updated': DataUpdatedEvent;
}
```

#### State Management Strategy
- **Global State**: [Shared state management approach]
- **Local State**: [Component-specific state management]
- **Server State**: [API data management strategy]
- **Persistent State**: [User preferences and settings]

### Routing Architecture

#### Hierarchical Routing
```
Shell Routes
├── /dashboard → Dashboard Microfrontend
├── /products → Product Catalog Microfrontend
│   ├── /products/:id → Product Detail
│   └── /products/search → Product Search
├── /orders → Order Management Microfrontend
└── /profile → User Profile Microfrontend
```

#### Route Configuration
```typescript
// Route Configuration
interface RouteConfig {
  path: string;
  microfrontend: string;
  component: string;
  guards?: AuthGuard[];
  preload?: boolean;
}
```

## Design System Integration

### Design Token Architecture
```
Global Tokens
├── Brand Colors
├── Typography Scale
├── Spacing System
├── Breakpoints
└── Animation Timings

Semantic Tokens
├── Component Colors
├── State Colors
├── Feedback Colors
└── Surface Colors

Component Tokens
├── Button Variants
├── Input Styles
├── Card Styles
└── Navigation Styles
```

### Component Library Strategy
- **Shared Components**: [List of shared UI components]
- **Component Distribution**: [How components are shared across microfrontends]
- **Version Management**: [Component versioning strategy]
- **Customization**: [Theme and customization approach]

## Performance Strategy

### Loading Performance
- **Code Splitting**: [Route and component-level splitting strategy]
- **Lazy Loading**: [Microfrontend lazy loading approach]
- **Bundle Optimization**: [Shared dependency optimization]
- **Caching Strategy**: [Multi-layer caching approach]

### Runtime Performance
- **Memory Management**: [Component lifecycle and cleanup]
- **Event Handling**: [Efficient event management]
- **State Updates**: [Optimized state update patterns]
- **Resource Cleanup**: [Resource management strategy]

### Performance Budgets
| Metric | Target | Measurement |
|--------|--------|-------------|
| First Contentful Paint | < 1.5s | Lighthouse |
| Largest Contentful Paint | < 2.5s | Core Web Vitals |
| First Input Delay | < 100ms | Core Web Vitals |
| Cumulative Layout Shift | < 0.1 | Core Web Vitals |
| Bundle Size | < 250KB | Webpack Bundle Analyzer |

## Security Architecture

### Authentication & Authorization
- **Authentication Flow**: [SSO/OAuth implementation]
- **Token Management**: [JWT/session token handling]
- **Authorization Model**: [RBAC/ABAC implementation]
- **Session Management**: [Session lifecycle and security]

### Security Controls
- **Content Security Policy**: [CSP configuration]
- **CORS Configuration**: [Cross-origin resource sharing]
- **Input Validation**: [Client-side validation strategy]
- **Secure Communication**: [HTTPS and secure headers]

## Deployment Architecture

### Deployment Strategy
- **Independent Deployment**: [How microfrontends deploy independently]
- **Versioning Strategy**: [Semantic versioning approach]
- **Rollback Procedures**: [Rollback and recovery strategies]
- **Environment Promotion**: [Dev/staging/production pipeline]

### Infrastructure Requirements
```
Production Environment
├── CDN Configuration
├── Load Balancer Setup
├── Container Orchestration
├── Monitoring Infrastructure
└── Backup and Recovery
```

### CI/CD Pipeline
```
Pipeline Stages
├── Code Quality Gates
├── Unit Testing
├── Integration Testing
├── Security Scanning
├── Performance Testing
├── Build and Package
├── Deployment
└── Post-Deployment Validation
```

## Monitoring and Observability

### Performance Monitoring
- **Core Web Vitals**: [Real User Monitoring setup]
- **Custom Metrics**: [Business-specific performance metrics]
- **Error Tracking**: [Error monitoring and alerting]
- **User Analytics**: [User behavior and journey tracking]

### Operational Monitoring
- **Health Checks**: [Microfrontend health monitoring]
- **Dependency Monitoring**: [External service monitoring]
- **Resource Utilization**: [Infrastructure monitoring]
- **Alert Configuration**: [Alert thresholds and escalation]

## Testing Strategy

### Testing Pyramid
- **Unit Tests (70%)**: [Component and utility testing]
- **Integration Tests (20%)**: [Cross-microfrontend integration]
- **E2E Tests (10%)**: [Critical user journey validation]

### Specialized Testing
- **Contract Testing**: [API and component contract validation]
- **Visual Testing**: [UI consistency and regression testing]
- **Performance Testing**: [Load and performance validation]
- **Accessibility Testing**: [WCAG compliance validation]

## Migration Strategy

### Migration Approach
- **Pattern**: [Strangler Fig/Big Bang/Incremental]
- **Timeline**: [Migration phases and milestones]
- **Risk Mitigation**: [Risk assessment and mitigation strategies]
- **Rollback Plan**: [Contingency and rollback procedures]

### Legacy Integration
- **Integration Points**: [How legacy systems integrate]
- **Data Migration**: [Data migration strategy]
- **User Experience**: [Maintaining UX during migration]
- **Team Transition**: [Team skill development and transition]

## Governance and Standards

### Development Standards
- **Coding Standards**: [Language and framework standards]
- **Component Standards**: [UI component development guidelines]
- **Testing Standards**: [Testing requirements and coverage]
- **Documentation Standards**: [Documentation requirements]

### Architecture Governance
- **Decision Framework**: [Architecture decision process]
- **Review Process**: [Regular architecture review schedule]
- **Compliance Monitoring**: [Automated compliance checking]
- **Exception Handling**: [Process for handling exceptions]

## Risk Assessment

### Technical Risks
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| [Risk Description] | [High/Medium/Low] | [High/Medium/Low] | [Mitigation Strategy] |

### Business Risks
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| [Risk Description] | [High/Medium/Low] | [High/Medium/Low] | [Mitigation Strategy] |

## Success Metrics and KPIs

### Technical Metrics
- **Performance**: [Core Web Vitals targets]
- **Reliability**: [Uptime and error rate targets]
- **Scalability**: [Performance under load targets]
- **Developer Experience**: [Build time and productivity metrics]

### Business Metrics
- **Time to Market**: [Feature delivery velocity]
- **User Experience**: [User satisfaction and engagement]
- **Operational Efficiency**: [Cost and resource optimization]
- **Innovation Rate**: [Technology adoption and experimentation]

## Appendices

### A. Technology Evaluation Matrix
[Detailed technology comparison and selection criteria]

### B. Performance Benchmarks
[Baseline performance measurements and targets]

### C. Security Assessment
[Security review and compliance checklist]

### D. Team Readiness Assessment
[Team skill assessment and training requirements]

---

*This template provides a comprehensive framework for documenting microfrontend architecture. Customize sections based on your specific project requirements and organizational standards.*

==================== END: microfrontend-architecture-tmpl ====================


==================== START: microfrontend-deployment-strategy-tmpl ====================
# Microfrontend Deployment Strategy Template

## Document Information
- **Document Type**: Microfrontend Deployment Strategy
- **Version**: 1.0
- **Status**: [Draft/Review/Approved]
- **Author**: [Microfrontend Architect Name]
- **Date**: [Creation Date]
- **Last Updated**: [Last Update Date]

## Executive Summary

### Deployment Overview
- **Project Name**: [Project Name]
- **Deployment Pattern**: [Independent/Coordinated/Hybrid]
- **Technology Stack**: [Next.js, Docker, Kubernetes, etc.]
- **Target Environments**: [Development/Staging/Production]
- **Deployment Frequency**: [Daily/Weekly/On-demand]

### Key Objectives
- **Independent Deployability**: Enable autonomous team deployments
- **Zero Downtime**: Maintain service availability during deployments
- **Rollback Capability**: Quick recovery from deployment issues
- **Performance Optimization**: Minimize deployment impact on user experience
- **Security Compliance**: Maintain security standards throughout deployment

## Deployment Architecture

### Infrastructure Overview
```
Production Environment
├── CDN Layer (Cloudflare/CloudFront)
│   ├── Static Assets
│   ├── Microfrontend Bundles
│   └── Edge Caching
├── Load Balancer (ALB/NGINX)
│   ├── SSL Termination
│   ├── Health Checks
│   └── Traffic Routing
├── Container Platform (Kubernetes/ECS)
│   ├── Shell Application Pods
│   ├── Microfrontend Pods
│   └── Supporting Services
└── Storage Layer
    ├── Container Registry
    ├── Asset Storage (S3)
    └── Configuration Store
```

### Deployment Environments
| Environment | Purpose | URL | Deployment Trigger |
|-------------|---------|-----|-------------------|
| Development | Feature development | [dev-url] | Feature branch push |
| Staging | Integration testing | [staging-url] | Main branch merge |
| Production | Live environment | [prod-url] | Release tag creation |

## Microfrontend Deployment Patterns

### Independent Deployment Strategy
```
Microfrontend A (Team Alpha)
├── Source Code Repository
├── CI/CD Pipeline
├── Container Registry
├── Deployment Manifest
└── Production Environment

Microfrontend B (Team Beta)
├── Source Code Repository
├── CI/CD Pipeline
├── Container Registry
├── Deployment Manifest
└── Production Environment
```

#### Benefits
- **Team Autonomy**: Teams deploy independently without coordination
- **Faster Delivery**: Reduced deployment bottlenecks
- **Risk Isolation**: Deployment failures don't affect other microfrontends
- **Technology Flexibility**: Teams can use different deployment technologies

#### Challenges
- **Integration Complexity**: Ensuring compatibility between versions
- **Monitoring Overhead**: Tracking multiple independent deployments
- **Shared Dependency Management**: Coordinating shared library updates

### Coordinated Deployment Strategy
```
Deployment Orchestrator
├── Dependency Analysis
├── Deployment Sequencing
├── Health Check Validation
├── Rollback Coordination
└── Notification System
```

#### Use Cases
- **Breaking Changes**: When API contracts change
- **Shared Dependency Updates**: Major library version updates
- **Security Patches**: Critical security updates across all microfrontends
- **Infrastructure Changes**: Platform or infrastructure updates

## CI/CD Pipeline Architecture

### Pipeline Stages
```
Source Code → Build → Test → Security → Package → Deploy → Validate
```

#### Stage 1: Source Code Management
```yaml
# GitHub Actions Workflow
name: Microfrontend CI/CD
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18.19.0'
  MICROFRONTEND_NAME: '[mf-name]'
```

#### Stage 2: Build Process
```yaml
build:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build microfrontend
      run: npm run build
      env:
        NODE_ENV: production
        MICROFRONTEND_NAME: ${{ env.MICROFRONTEND_NAME }}
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: dist/
```

#### Stage 3: Testing
```yaml
test:
  runs-on: ubuntu-latest
  needs: build
  strategy:
    matrix:
      test-type: [unit, integration, e2e]
  steps:
    - name: Run ${{ matrix.test-type }} tests
      run: npm run test:${{ matrix.test-type }}
    
    - name: Upload test results
      uses: actions/upload-artifact@v4
      with:
        name: test-results-${{ matrix.test-type }}
        path: test-results/
```

#### Stage 4: Security Scanning
```yaml
security:
  runs-on: ubuntu-latest
  steps:
    - name: Run security audit
      run: npm audit --audit-level moderate
    
    - name: Scan for vulnerabilities
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: security-scan-results.sarif
```

#### Stage 5: Container Build
```yaml
container:
  runs-on: ubuntu-latest
  needs: [test, security]
  steps:
    - name: Build Docker image
      run: |
        docker build -t ${{ env.MICROFRONTEND_NAME }}:${{ github.sha }} .
        docker tag ${{ env.MICROFRONTEND_NAME }}:${{ github.sha }} \
                   ${{ env.MICROFRONTEND_NAME }}:latest
    
    - name: Push to registry
      run: |
        docker push ${{ env.MICROFRONTEND_NAME }}:${{ github.sha }}
        docker push ${{ env.MICROFRONTEND_NAME }}:latest
```

### Deployment Automation
```yaml
deploy:
  runs-on: ubuntu-latest
  needs: container
  environment: ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}
  steps:
    - name: Deploy to Kubernetes
      run: |
        kubectl set image deployment/${{ env.MICROFRONTEND_NAME }} \
          ${{ env.MICROFRONTEND_NAME }}=${{ env.MICROFRONTEND_NAME }}:${{ github.sha }}
        kubectl rollout status deployment/${{ env.MICROFRONTEND_NAME }}
    
    - name: Validate deployment
      run: |
        kubectl get pods -l app=${{ env.MICROFRONTEND_NAME }}
        curl -f ${{ env.HEALTH_CHECK_URL }} || exit 1
```

## Container Strategy

### Dockerfile Template
```dockerfile
# Multi-stage build for microfrontend
FROM node:18.19.0-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine AS production

# Copy built assets
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Kubernetes Deployment Manifest
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: [microfrontend-name]
  labels:
    app: [microfrontend-name]
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: [microfrontend-name]
  template:
    metadata:
      labels:
        app: [microfrontend-name]
        version: v1
    spec:
      containers:
      - name: [microfrontend-name]
        image: [registry]/[microfrontend-name]:latest
        ports:
        - containerPort: 80
        env:
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: [microfrontend-name]-service
spec:
  selector:
    app: [microfrontend-name]
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: ClusterIP
```

## Deployment Strategies

### Blue-Green Deployment
```
Blue Environment (Current)     Green Environment (New)
├── Load Balancer 100%        ├── Load Balancer 0%
├── Version 1.0               ├── Version 1.1
├── Active Traffic            ├── No Traffic
└── Monitoring Active         └── Pre-deployment Testing

Switch Traffic:
Blue Environment (Previous)   Green Environment (Current)
├── Load Balancer 0%          ├── Load Balancer 100%
├── Version 1.0               ├── Version 1.1
├── Standby for Rollback      ├── Active Traffic
└── Monitoring Standby        └── Monitoring Active
```

### Canary Deployment
```
Deployment Phases:
Phase 1: 5% traffic to new version
├── Monitor metrics for 15 minutes
├── Validate error rates < 0.1%
└── Check performance metrics

Phase 2: 25% traffic to new version
├── Monitor metrics for 30 minutes
├── Validate user experience metrics
└── Check business metrics

Phase 3: 50% traffic to new version
├── Monitor metrics for 1 hour
├── Validate full functionality
└── Check integration points

Phase 4: 100% traffic to new version
├── Complete deployment
├── Remove old version
└── Update monitoring baselines
```

### Rolling Deployment
```
Rolling Update Strategy:
├── Max Unavailable: 25%
├── Max Surge: 25%
├── Update Strategy: RollingUpdate
└── Rollback on Failure: Automatic

Update Sequence:
1. Create new pod with updated image
2. Wait for pod to be ready
3. Terminate one old pod
4. Repeat until all pods updated
5. Validate deployment success
```

## Monitoring and Validation

### Deployment Metrics
```typescript
interface DeploymentMetrics {
  deploymentId: string;
  microfrontend: string;
  version: string;
  startTime: Date;
  endTime?: Date;
  status: 'in-progress' | 'success' | 'failed' | 'rolled-back';
  healthChecks: HealthCheck[];
  performanceMetrics: PerformanceMetrics;
}

interface HealthCheck {
  endpoint: string;
  status: number;
  responseTime: number;
  timestamp: Date;
}
```

### Automated Validation
```bash
#!/bin/bash
# Post-deployment validation script

MICROFRONTEND_NAME="[mf-name]"
HEALTH_ENDPOINT="https://[domain]/api/health"
TIMEOUT=300 # 5 minutes

echo "Starting post-deployment validation for $MICROFRONTEND_NAME"

# Health check validation
for i in {1..10}; do
  if curl -f "$HEALTH_ENDPOINT" > /dev/null 2>&1; then
    echo "Health check passed (attempt $i)"
    break
  else
    echo "Health check failed (attempt $i), retrying in 30s..."
    sleep 30
  fi
  
  if [ $i -eq 10 ]; then
    echo "Health check failed after 10 attempts"
    exit 1
  fi
done

# Performance validation
echo "Running performance validation..."
lighthouse --chrome-flags="--headless" --output=json --output-path=./lighthouse-report.json "$HEALTH_ENDPOINT"

# Extract Core Web Vitals
LCP=$(jq '.audits["largest-contentful-paint"].numericValue' lighthouse-report.json)
FID=$(jq '.audits["max-potential-fid"].numericValue' lighthouse-report.json)
CLS=$(jq '.audits["cumulative-layout-shift"].numericValue' lighthouse-report.json)

# Validate against thresholds
if (( $(echo "$LCP > 2500" | bc -l) )); then
  echo "LCP threshold exceeded: $LCP ms"
  exit 1
fi

echo "Deployment validation completed successfully"
```

## Rollback Procedures

### Automatic Rollback Triggers
```yaml
# Rollback conditions
rollback_conditions:
  - error_rate > 1%
  - response_time_p95 > 2000ms
  - health_check_failures > 3
  - user_reported_issues > 10
  - business_metric_drop > 5%
```

### Rollback Execution
```bash
#!/bin/bash
# Automated rollback script

MICROFRONTEND_NAME="[mf-name]"
PREVIOUS_VERSION="[previous-version]"

echo "Initiating rollback for $MICROFRONTEND_NAME to version $PREVIOUS_VERSION"

# Kubernetes rollback
kubectl rollout undo deployment/$MICROFRONTEND_NAME

# Wait for rollback completion
kubectl rollout status deployment/$MICROFRONTEND_NAME --timeout=300s

# Validate rollback
if curl -f "https://[domain]/api/health" > /dev/null 2>&1; then
  echo "Rollback completed successfully"
  # Notify teams
  curl -X POST "$SLACK_WEBHOOK" -d "{\"text\":\"Rollback completed for $MICROFRONTEND_NAME\"}"
else
  echo "Rollback validation failed"
  exit 1
fi
```

## Security Considerations

### Deployment Security
- **Image Scanning**: Container vulnerability scanning before deployment
- **Secret Management**: Secure handling of API keys and certificates
- **Network Security**: Secure communication between services
- **Access Control**: Role-based access to deployment systems
- **Audit Logging**: Complete audit trail of deployment activities

### Runtime Security
```yaml
# Security context for containers
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  readOnlyRootFilesystem: true
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
```

## Performance Optimization

### Build Optimization
```javascript
// Webpack optimization for microfrontends
module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        shared: {
          name: 'shared',
          minChunks: 2,
          chunks: 'all',
        },
      },
    },
  },
  plugins: [
    new ModuleFederationPlugin({
      name: '[microfrontend-name]',
      filename: 'remoteEntry.js',
      exposes: {
        './Component': './src/Component',
      },
      shared: {
        react: { singleton: true },
        'react-dom': { singleton: true },
      },
    }),
  ],
};
```

### CDN Configuration
```nginx
# NGINX configuration for microfrontend assets
location /static/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
}

location /remoteEntry.js {
    expires 5m;
    add_header Cache-Control "public, max-age=300";
}
```

## Disaster Recovery

### Backup Strategy
- **Code Repository**: Git-based version control with multiple remotes
- **Container Images**: Multi-region container registry replication
- **Configuration**: Infrastructure as Code with version control
- **Data**: Regular backup of configuration and state data

### Recovery Procedures
1. **Assess Impact**: Determine scope and severity of the incident
2. **Activate Team**: Notify incident response team
3. **Isolate Issue**: Prevent further damage or data loss
4. **Restore Service**: Execute recovery procedures
5. **Validate Recovery**: Confirm service restoration
6. **Post-Incident Review**: Analyze and improve procedures

## Compliance and Governance

### Deployment Governance
- **Change Approval**: Required approvals for production deployments
- **Documentation**: Mandatory deployment documentation
- **Testing Requirements**: Minimum testing coverage and validation
- **Security Review**: Security assessment for significant changes
- **Compliance Validation**: Regulatory compliance verification

### Audit Requirements
```json
{
  "deployment_audit": {
    "deployment_id": "uuid",
    "microfrontend": "string",
    "version": "string",
    "deployed_by": "string",
    "approved_by": "string",
    "deployment_time": "iso8601",
    "validation_results": "object",
    "rollback_plan": "string"
  }
}
```

---

*This template provides a comprehensive framework for microfrontend deployment strategy. Customize based on your infrastructure, security requirements, and organizational policies.*

==================== END: microfrontend-deployment-strategy-tmpl ====================


==================== START: platform-engineering-strategy-tmpl ====================
# Platform Engineering Strategy: {Platform Name}
## Internal Developer Platform Design and Implementation Framework

### Document Information
- **Platform Name:** {Platform Name}
- **Document Version:** 1.0
- **Creation Date:** {Date}
- **Platform Team:** {Team Name}
- **Last Updated:** {Date}

---

## 1. Platform Vision and Strategy

### Platform Mission Statement
{Define the platform's purpose, value proposition, and strategic objectives}

### Platform-as-a-Product Approach
- **Product Vision:** {Long-term vision for the platform}
- **Target Users:** {Development teams, operations teams, business stakeholders}
- **Value Proposition:** {Key benefits and value delivered to users}
- **Success Metrics:** {Platform adoption, developer productivity, operational efficiency}

### Strategic Objectives
- **Developer Experience:** {Improve developer productivity and satisfaction}
- **Operational Excellence:** {Enhance system reliability and operational efficiency}
- **Business Enablement:** {Accelerate business value delivery and innovation}
- **Cost Optimization:** {Optimize infrastructure costs and resource utilization}

---

## 2. Platform Architecture and Components

### Core Platform Components
```yaml
Platform Architecture:
  Developer Portal:
    - Service Catalog: {Self-service capabilities and golden paths}
    - Documentation Hub: {Comprehensive documentation and guides}
    - Developer Tools: {Integrated development and deployment tools}
    - Metrics Dashboard: {Developer productivity and platform metrics}
  
  Infrastructure Layer:
    - Container Orchestration: {Kubernetes clusters and management}
    - Service Mesh: {Service communication and security}
    - API Gateway: {External API management and routing}
    - Load Balancing: {Traffic distribution and scaling}
  
  Platform Services:
    - CI/CD Pipeline: {Automated build, test, and deployment}
    - Configuration Management: {Centralized configuration and secrets}
    - Monitoring & Observability: {Comprehensive system monitoring}
    - Security & Compliance: {Automated security and compliance checks}
  
  Data Platform:
    - Data Pipeline: {Data ingestion and processing}
    - Analytics Platform: {Business intelligence and reporting}
    - AI/ML Platform: {Machine learning and AI capabilities}
    - Data Governance: {Data quality and compliance management}
```

### Technology Stack
```yaml
Core Technologies:
  Container Platform:
    - Kubernetes: {Container orchestration and management}
    - Docker: {Containerization and image management}
    - Helm: {Package management and deployment}
  
  Service Mesh:
    - Istio/Linkerd: {Service communication and security}
    - Envoy Proxy: {Load balancing and traffic management}
  
  CI/CD Platform:
    - GitLab/GitHub Actions: {Source control and automation}
    - ArgoCD/Flux: {GitOps and continuous deployment}
    - Tekton/Jenkins: {Build and deployment pipelines}
  
  Observability Stack:
    - Prometheus/Grafana: {Metrics and monitoring}
    - Jaeger/Zipkin: {Distributed tracing}
    - ELK/Loki: {Logging and log analysis}
  
  Cloud Services:
    - AWS/Azure/GCP: {Cloud infrastructure and services}
    - Terraform: {Infrastructure as code}
    - Vault: {Secrets management}
```

---

## 3. Developer Experience Design

### Golden Paths and Self-Service Capabilities
```yaml
Golden Paths:
  Service Development:
    - Service Templates: {Pre-configured service templates and scaffolding}
    - Development Environment: {Local development setup and tools}
    - Testing Framework: {Automated testing and quality gates}
    - Deployment Pipeline: {Automated deployment and rollback}
  
  Data Engineering:
    - Data Pipeline Templates: {ETL/ELT pipeline templates}
    - Analytics Dashboards: {Self-service analytics and reporting}
    - ML Model Deployment: {Machine learning model serving}
  
  Frontend Development:
    - Micro-Frontend Framework: {Frontend development and deployment}
    - Design System: {UI components and design guidelines}
    - Performance Optimization: {Frontend performance and optimization}
```

### Developer Portal Features
- **Service Catalog:** {Comprehensive catalog of available services and APIs}
- **Documentation Hub:** {Centralized documentation with search and navigation}
- **Getting Started Guides:** {Onboarding and quick-start documentation}
- **API Explorer:** {Interactive API documentation and testing}
- **Metrics Dashboard:** {Developer productivity and service health metrics}
- **Support and Feedback:** {Help desk integration and feedback collection}

### Developer Productivity Tools
- **IDE Integration:** {Integrated development environment plugins and extensions}
- **Local Development:** {Local development environment setup and management}
- **Debugging Tools:** {Distributed debugging and troubleshooting tools}
- **Performance Profiling:** {Application performance monitoring and profiling}

---

## 4. Operational Excellence Framework

### Site Reliability Engineering (SRE) Practices
```yaml
SRE Implementation:
  Service Level Objectives:
    - Availability SLO: {Target uptime and reliability metrics}
    - Latency SLO: {Response time and performance targets}
    - Error Rate SLO: {Error rate and quality thresholds}
  
  Error Budget Management:
    - Error Budget Policy: {Error budget allocation and management}
    - Budget Monitoring: {Real-time error budget tracking}
    - Budget Exhaustion: {Procedures when error budget is exhausted}
  
  Incident Management:
    - Incident Response: {Incident detection and response procedures}
    - Post-Mortem Process: {Incident analysis and learning}
    - Runbook Automation: {Automated incident response and remediation}
```

### Monitoring and Observability
- **Infrastructure Monitoring:** {Server, network, and resource monitoring}
- **Application Monitoring:** {Service health and performance monitoring}
- **Business Monitoring:** {Business metrics and KPI tracking}
- **Security Monitoring:** {Security event detection and response}

### Capacity Planning and Scaling
- **Resource Monitoring:** {CPU, memory, storage, and network utilization}
- **Auto-Scaling Policies:** {Horizontal and vertical scaling automation}
- **Capacity Forecasting:** {Predictive capacity planning and resource allocation}
- **Cost Optimization:** {Resource optimization and cost management}

---

## 5. Security and Compliance Integration

### Security-by-Design Principles
```yaml
Security Framework:
  Identity and Access Management:
    - Authentication: {Multi-factor authentication and SSO integration}
    - Authorization: {Role-based access control and permissions}
    - Service Identity: {Service-to-service authentication and mTLS}
  
  Security Automation:
    - Vulnerability Scanning: {Automated security scanning and remediation}
    - Compliance Checking: {Automated compliance validation}
    - Security Policies: {Policy-as-code and automated enforcement}
  
  Data Protection:
    - Encryption: {Data encryption at rest and in transit}
    - Data Classification: {Data sensitivity classification and handling}
    - Privacy Controls: {Data privacy and GDPR compliance}
```

### Compliance Automation
- **Policy as Code:** {Automated policy enforcement and validation}
- **Audit Trails:** {Comprehensive audit logging and compliance reporting}
- **Regulatory Compliance:** {SOX, GDPR, HIPAA, and industry-specific compliance}
- **Risk Management:** {Risk assessment and mitigation automation}

---

## 6. Platform Team Organization

### Team Topology and Responsibilities
```yaml
Platform Team Structure:
  Platform Product Manager:
    - Platform Strategy: {Product vision and roadmap}
    - User Research: {Developer needs and feedback analysis}
    - Stakeholder Management: {Cross-team coordination and communication}
  
  Platform Engineers:
    - Infrastructure: {Platform infrastructure and automation}
    - Developer Tools: {Tool development and integration}
    - Observability: {Monitoring and alerting systems}
  
  Site Reliability Engineers:
    - System Reliability: {Platform reliability and performance}
    - Incident Response: {Incident management and resolution}
    - Capacity Planning: {Resource planning and optimization}
  
  Security Engineers:
    - Security Architecture: {Security design and implementation}
    - Compliance: {Regulatory compliance and audit support}
    - Threat Detection: {Security monitoring and response}
```

### Team Interaction Patterns
- **Platform Team as Enabler:** {Enable development teams with tools and capabilities}
- **Collaboration Model:** {Regular interaction and feedback with development teams}
- **Support Model:** {Platform support and troubleshooting assistance}
- **Training and Onboarding:** {Developer training and platform adoption}

---

## 7. Service Catalog and Templates

### Service Templates
```yaml
Template Categories:
  Microservice Templates:
    - REST API Service: {Standard REST API service template}
    - Event-Driven Service: {Event processing service template}
    - Data Processing Service: {Batch and stream processing template}
    - AI/ML Service: {Machine learning service template}
  
  Infrastructure Templates:
    - Database Service: {Database deployment and management}
    - Message Queue: {Message broker and queue management}
    - Cache Service: {Caching layer and Redis deployment}
    - Storage Service: {Object storage and file management}
  
  Frontend Templates:
    - Micro-Frontend: {Frontend application template}
    - Static Website: {Static site deployment template}
    - Progressive Web App: {PWA development template}
```

### Template Standards
- **Code Quality:** {Linting, formatting, and code quality standards}
- **Security Standards:** {Security best practices and vulnerability prevention}
- **Performance Standards:** {Performance optimization and monitoring}
- **Documentation Standards:** {API documentation and code documentation}

---

## 8. AI and Machine Learning Platform

### ML Platform Capabilities
```yaml
ML Platform Components:
  Model Development:
    - Jupyter Notebooks: {Interactive development environment}
    - Experiment Tracking: {ML experiment management and versioning}
    - Feature Store: {Feature engineering and management}
  
  Model Deployment:
    - Model Serving: {Model deployment and serving infrastructure}
    - A/B Testing: {Model performance testing and comparison}
    - Model Monitoring: {Model performance and drift monitoring}
  
  Data Pipeline:
    - Data Ingestion: {Data collection and preprocessing}
    - Feature Engineering: {Automated feature extraction and transformation}
    - Data Quality: {Data validation and quality monitoring}
```

### AI Governance Integration
- **Model Governance:** {Model approval and deployment governance}
- **Bias Detection:** {Automated bias detection and mitigation}
- **Explainability:** {Model interpretability and explanation}
- **Compliance:** {AI regulatory compliance and audit trails}

---

## 9. Implementation Roadmap

### Phase 1: Foundation (Months 1-3)
```yaml
Foundation Phase:
  Infrastructure Setup:
    - Kubernetes cluster deployment and configuration
    - Basic CI/CD pipeline implementation
    - Monitoring and logging infrastructure
  
  Core Services:
    - Authentication and authorization service
    - Configuration management service
    - Basic developer portal
```

### Phase 2: Developer Experience (Months 4-6)
```yaml
Developer Experience Phase:
  Self-Service Capabilities:
    - Service catalog and templates
    - Automated deployment pipelines
    - Developer documentation portal
  
  Golden Paths:
    - Microservice development workflow
    - Database and storage provisioning
    - Testing and quality gates
```

### Phase 3: Advanced Capabilities (Months 7-12)
```yaml
Advanced Capabilities Phase:
  AI/ML Platform:
    - Machine learning pipeline automation
    - Model serving and monitoring
    - AI governance and compliance
  
  Advanced Observability:
    - Distributed tracing implementation
    - Advanced analytics and alerting
    - Capacity planning automation
```

---

## 10. Success Metrics and KPIs

### Developer Productivity Metrics
- **Deployment Frequency:** {Number of deployments per day/week}
- **Lead Time:** {Time from code commit to production deployment}
- **Mean Time to Recovery:** {Time to recover from incidents}
- **Developer Satisfaction:** {Developer experience and satisfaction scores}

### Platform Performance Metrics
- **Platform Availability:** {Platform uptime and reliability}
- **Service Adoption:** {Number of services using platform capabilities}
- **Cost Efficiency:** {Infrastructure cost per service/developer}
- **Security Compliance:** {Security incident rate and compliance scores}

### Business Impact Metrics
- **Time to Market:** {Reduced time to deliver new features}
- **Innovation Rate:** {Number of new services and capabilities delivered}
- **Operational Efficiency:** {Reduced operational overhead and manual work}
- **Risk Reduction:** {Reduced security incidents and compliance violations}

---

## 11. Change Management and Evolution

### Platform Evolution Strategy
- **Continuous Improvement:** {Regular platform capability enhancement}
- **Technology Refresh:** {Technology stack updates and migrations}
- **Capability Expansion:** {New platform capabilities and services}
- **User Feedback Integration:** {Developer feedback and requirement integration}

### Change Management Process
- **RFC Process:** {Request for Comments and change proposal process}
- **Impact Assessment:** {Change impact analysis and risk assessment}
- **Migration Planning:** {Platform migration and upgrade planning}
- **Communication Strategy:** {Change communication and training}

---

## 12. Documentation and Training

### Documentation Strategy
- **Platform Documentation:** {Comprehensive platform documentation}
- **API Documentation:** {Service and API documentation}
- **Runbooks:** {Operational procedures and troubleshooting guides}
- **Architecture Decision Records:** {Design decisions and rationale}

### Training and Onboarding
- **Developer Onboarding:** {New developer platform training}
- **Platform Training:** {Advanced platform capabilities training}
- **Best Practices:** {Development and operational best practices}
- **Certification Programs:** {Platform expertise certification}

---

## 13. Risk Management and Mitigation

### Risk Assessment
- **Technical Risks:** {Technology adoption and implementation risks}
- **Operational Risks:** {Platform availability and performance risks}
- **Security Risks:** {Security vulnerabilities and compliance risks}
- **Organizational Risks:** {Change management and adoption risks}

### Mitigation Strategies
- **Risk Monitoring:** {Continuous risk assessment and monitoring}
- **Contingency Planning:** {Backup plans and alternative approaches}
- **Incident Response:** {Risk incident response and recovery procedures}
- **Regular Reviews:** {Periodic risk assessment and strategy updates}

---

## 14. Change Log

| Version | Date | Changes | Author | Approver |
|---------|------|---------|--------|----------|
| 1.0 | {Date} | Initial platform strategy | {Author} | {Approver} |

---

## 15. References and Dependencies

- **Architecture Documents:** {Links to system architecture documentation}
- **Technology Standards:** {Links to technology standards and guidelines}
- **Security Policies:** {Links to security and compliance policies}
- **Vendor Documentation:** {Links to vendor and technology documentation}

==================== END: platform-engineering-strategy-tmpl ====================


==================== START: prd-tmpl ====================
# {Project Name} Product Requirements Document (PRD)

## Goal, Objective and Context

This should come mostly from the user or the provided brief, but ask for clarifications as needed.

## Functional Requirements (MVP)

You should have a good idea at this point, but clarify suggest question and explain to ensure these are correct.

## Non Functional Requirements (MVP)

You should have a good idea at this point, but clarify suggest question and explain to ensure these are correct.

## User Interaction and Design Goals

{
If the product includes a User Interface (UI), this section captures the Product Manager's high-level vision and goals for the User Experience (UX). This information will serve as a crucial starting point and brief for the Design Architect.

Consider and elicit information from the user regarding:

- **Overall Vision & Experience:** What is the desired look and feel (e.g., "modern and minimalist," "friendly and approachable," "data-intensive and professional")? What kind of experience should users have?
- **Key Interaction Paradigms:** Are there specific ways users will interact with core features (e.g., "drag-and-drop interface for X," "wizard-style setup for Y," "real-time dashboard for Z")?
- **Core Screens/Views (Conceptual):** From a product perspective, what are the most critical screens or views necessary to deliver the MVP's value? (e.g., "Login Screen," "Main Dashboard," "Item Detail Page," "Settings Page").
- **Accessibility Aspirations:** Any known high-level accessibility goals (e.g., "must be usable by screen reader users").
- **Branding Considerations (High-Level):** Any known branding elements or style guides that must be incorporated?
- **Target Devices/Platforms:** (e.g., "primarily web desktop," "mobile-first responsive web app").

This section is not intended to be a detailed UI specification but rather a product-focused brief to guide the subsequent detailed work by the Design Architect, who will create the comprehensive UI/UX Specification document.
}

## Technical Assumptions

This is where we can list information mostly to be used by the architect to produce the technical details. This could be anything we already know or found out from the user at a technical high level. Inquire about this from the user to get a basic idea of languages, frameworks, knowledge of starter templates, libraries, external apis, potential library choices etc...

- **Repository & Service Architecture:** {CRITICAL DECISION: Document the chosen repository structure (e.g., Monorepo, Polyrepo) and the high-level service architecture (e.g., Monolith, Microservices, Serverless functions within a Monorepo). Explain the rationale based on project goals, MVP scope, team structure, and scalability needs. This decision directly impacts the technical approach and informs the Architect Agent.}

### Testing requirements

How will we validate functionality beyond unit testing? Will we want manual scripts or testing, e2e, integration etc... figure this out from the user to populate this section

## Epic Overview

- **Epic {#}: {Title}**
  - Goal: {A concise 1-2 sentence statement describing the primary objective and value of this Epic.}
  - Story {#}: As a {type of user/system}, I want {to perform an action / achieve a goal} so that {I can realize a benefit / achieve a reason}.
    - {Acceptance Criteria List}
  - Story {#}: As a {type of user/system}, I want {to perform an action / achieve a goal} so that {I can realize a benefit / achieve a reason}.
    - {Acceptance Criteria List}
- **Epic {#}: {Title}**
  - Goal: {A concise 1-2 sentence statement describing the primary objective and value of this Epic.}
  - Story {#}: As a {type of user/system}, I want {to perform an action / achieve a goal} so that {I can realize a benefit / achieve a reason}.
    - {Acceptance Criteria List}
  - Story {#}: As a {type of user/system}, I want {to perform an action / achieve a goal} so that {I can realize a benefit / achieve a reason}.
    - {Acceptance Criteria List}

## Key Reference Documents

{ This section will be created later, from the sections prior to this being carved up into smaller documents }

## Out of Scope Ideas Post MVP

Anything you and the user agreed it out of scope or can be removed from scope to keep MVP lean. Consider the goals of the PRD and what might be extra gold plating or additional features that could wait until the MVP is completed and delivered to assess functionality and market fit or usage.

## [OPTIONAL: For Simplified PM-to-Development Workflow Only] Core Technical Decisions & Application Structure

{This section is to be populated ONLY if the PM is operating in the 'Simplified PM-to-Development Workflow'. It captures essential technical foundations that would typically be defined by an Architect, allowing for a more direct path to development. This information should be gathered after initial PRD sections (Goals, Users, etc.) are drafted, and ideally before or in parallel with detailed Epic/Story definition, and updated as needed.}

### Technology Stack Selections

{Collaboratively define the core technologies. Be specific about choices and versions where appropriate.}

- **Primary Backend Language/Framework:** {e.g., Python/FastAPI, Node.js/Express, Java/Spring Boot}
- **Primary Frontend Language/Framework (if applicable):** {e.g., TypeScript/React (Next.js), JavaScript/Vue.js}
- **Database:** {e.g., PostgreSQL, MongoDB, AWS DynamoDB}
- **Key Libraries/Services (Backend):** {e.g., Authentication (JWT, OAuth provider), ORM (SQLAlchemy), Caching (Redis)}
- **Key Libraries/Services (Frontend, if applicable):** {e.g., UI Component Library (Material-UI, Tailwind CSS + Headless UI), State Management (Redux, Zustand)}
- **Deployment Platform/Environment:** {e.g., Docker on AWS ECS, Vercel, Netlify, Kubernetes}
- **Version Control System:** {e.g., Git with GitHub/GitLab}

### Proposed Application Structure

{Describe the high-level organization of the codebase. This might include a simple text-based directory layout, a list of main modules/components, and a brief explanation of how they interact. The goal is to provide a clear starting point for developers.}

Example:

```
/
├── app/                  # Main application source code
│   ├── api/              # Backend API routes and logic
│   │   ├── v1/
│   │   └── models.py
│   ├── web/              # Frontend components and pages (if monolithic)
│   │   ├── components/
│   │   └── pages/
│   ├── core/             # Shared business logic, utilities
│   └── main.py           # Application entry point
├── tests/                # Unit and integration tests
├── scripts/              # Utility scripts
├── Dockerfile
├── requirements.txt
└── README.md
```

- **Monorepo/Polyrepo:** {Specify if a monorepo or polyrepo structure is envisioned, and briefly why.}
- **Key Modules/Components and Responsibilities:**
  - {Module 1 Name}: {Brief description of its purpose and key responsibilities}
  - {Module 2 Name}: {Brief description of its purpose and key responsibilities}
  - ...
- **Data Flow Overview (Conceptual):** {Briefly describe how data is expected to flow between major components, e.g., Frontend -> API -> Core Logic -> Database.}

## Change Log

| Change | Date | Version | Description | Author |
| ------ | ---- | ------- | ----------- | ------ |

----- END PRD START CHECKLIST OUTPUT ------

## Checklist Results Report

----- END Checklist START Design Architect `UI/UX Specification Mode` Prompt ------

----- END Design Architect `UI/UX Specification Mode` Prompt START Architect Prompt ------

## Initial Architect Prompt

Based on our discussions and requirements analysis for the {Product Name}, I've compiled the following technical guidance to inform your architecture analysis and decisions to kick off Architecture Creation Mode:

### Technical Infrastructure

- **Repository & Service Architecture Decision:** {Reiterate the decision made in 'Technical Assumptions', e.g., Monorepo with Next.js frontend and Python FastAPI backend services within the same repo; or Polyrepo with separate Frontend (Next.js) and Backend (Spring Boot Microservices) repositories.}
- **Starter Project/Template:** {Information about any starter projects, templates, or existing codebases that should be used}
- **Hosting/Cloud Provider:** {Specified cloud platform (AWS, Azure, GCP, etc.) or hosting requirements}
- **Frontend Platform:** {Framework/library preferences or requirements (React, Angular, Vue, etc.)}
- **Backend Platform:** {Framework/language preferences or requirements (Node.js, Python/Django, etc.)}
- **Database Requirements:** {Relational, NoSQL, specific products or services preferred}

### Technical Constraints

- {List any technical constraints that impact architecture decisions}
- {Include any mandatory technologies, services, or platforms}
- {Note any integration requirements with specific technical implications}

### Deployment Considerations

- {Deployment frequency expectations}
- {CI/CD requirements}
- {Environment requirements (local, dev, staging, production)}

### Local Development & Testing Requirements

{Include this section only if the user has indicated these capabilities are important. If not applicable based on user preferences, you may remove this section.}

- {Requirements for local development environment}
- {Expectations for command-line testing capabilities}
- {Needs for testing across different environments}
- {Utility scripts or tools that should be provided}
- {Any specific testability requirements for components}

### Other Technical Considerations

- {Security requirements with technical implications}
- {Scalability needs with architectural impact}
- {Any other technical context the Architect should consider}

----- END Architect Prompt -----

==================== END: prd-tmpl ====================


==================== START: project-brief-tmpl ====================
# Project Brief: {Project Name}

## Introduction / Problem Statement

{Describe the core idea, the problem being solved, or the opportunity being addressed. Why is this project needed?}

## Vision & Goals

- **Vision:** {Describe the high-level desired future state or impact of this project.}
- **Primary Goals:** {List 2-5 specific, measurable, achievable, relevant, time-bound (SMART) goals for the Minimum Viable Product (MVP).}
  - Goal 1: ...
  - Goal 2: ...
- **Success Metrics (Initial Ideas):** {How will we measure if the project/MVP is successful? List potential KPIs.}

## Target Audience / Users

{Describe the primary users of this product/system. Who are they? What are their key characteristics or needs relevant to this project?}

## Key Features / Scope (High-Level Ideas for MVP)

{List the core functionalities or features envisioned for the MVP. Keep this high-level; details will go in the PRD/Epics.}

- Feature Idea 1: ...
- Feature Idea 2: ...
- Feature Idea N: ...

## Post MVP Features / Scope and Ideas

{List the core functionalities or features envisioned as potential for POST MVP. Keep this high-level; details will go in the PRD/Epics/Architecture.}

- Feature Idea 1: ...
- Feature Idea 2: ...
- Feature Idea N: ...

## Known Technical Constraints or Preferences

- **Constraints:** {List any known limitations and technical mandates or preferences - e.g., budget, timeline, specific technology mandates, required integrations, compliance needs.}
- **Initial Architectural Preferences (if any):** {Capture any early thoughts or strong preferences regarding repository structure (e.g., monorepo, polyrepo) and overall service architecture (e.g., monolith, microservices, serverless components). This is not a final decision point but for initial awareness.}
- **Risks:** {Identify potential risks - e.g., technical challenges, resource availability, market acceptance, dependencies.}
- **User Preferences:** {Any specific requests from the user that are not a high level feature that could direct technology or library choices, or anything else that came up in the brainstorming or drafting of the PRD that is not included in prior document sections}

## Relevant Research (Optional)

{Link to or summarize findings from any initial research conducted (e.g., `deep-research-report-BA.md`).}

## PM Prompt

This Project Brief provides the full context for {Project Name}. Please start in 'PRD Generation Mode', review the brief thoroughly to work with the user to create the PRD section by section 1 at a time, asking for any necessary clarification or suggesting improvements as your mode 1 programming allows.

<example_handoff_prompt>
This Project Brief provides the full context for Mealmate. Please start in 'PRD Generation Mode', review the brief thoroughly to work with the user to create the PRD section by section 1 at a time, asking for any necessary clarification or suggesting improvements as your mode 1 programming allows.</example_handoff_prompt>

==================== END: project-brief-tmpl ====================


==================== START: service-integration-contract-tmpl ====================
# Service Integration Contract: {Service A} ↔ {Service B}
## Cross-Service Communication and Dependency Specification

### Document Information
- **Contract ID:** {Unique Contract Identifier}
- **Service A:** {Provider Service Name and Version}
- **Service B:** {Consumer Service Name and Version}
- **Contract Version:** 1.0
- **Creation Date:** {Date}
- **Owner Teams:** {Provider Team} ↔ {Consumer Team}
- **Last Updated:** {Date}

---

## 1. Integration Overview

### Integration Purpose
{Clear description of why these services need to communicate and what business value this integration provides}

### Communication Pattern
- [ ] **Synchronous (Request-Response)** - Direct API calls with immediate response
- [ ] **Asynchronous (Event-Driven)** - Event publishing and consumption
- [ ] **Hybrid** - Combination of both patterns

### Integration Type
- [ ] **Data Sharing** - Sharing data between services
- [ ] **Workflow Coordination** - Coordinating business processes
- [ ] **Event Notification** - Notifying of state changes
- [ ] **Command Execution** - Requesting actions from other services

---

## 2. API Contract Specifications (Synchronous)

### Endpoint Definitions
```yaml
# Provider Service API Endpoints
GET /api/v1/{resource}:
  description: {Endpoint purpose}
  parameters:
    - name: {param_name}
      type: {param_type}
      required: {true/false}
  responses:
    200:
      description: Success
      schema: {ResponseSchema}
    400:
      description: Bad Request
    404:
      description: Not Found
    500:
      description: Internal Server Error

POST /api/v1/{resource}:
  description: {Endpoint purpose}
  request_body:
    schema: {RequestSchema}
  responses:
    201:
      description: Created
      schema: {ResponseSchema}
```

### Request/Response Schemas
```json
// Request Schema Example
{
  "field1": "string",
  "field2": "integer",
  "field3": {
    "nested_field": "string"
  }
}

// Response Schema Example
{
  "id": "string",
  "status": "string",
  "data": {
    "result_field": "string"
  },
  "metadata": {
    "timestamp": "ISO8601",
    "version": "string"
  }
}
```

### Error Handling Contract
```json
// Standard Error Response Format
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "Additional error context",
    "timestamp": "ISO8601",
    "trace_id": "string"
  }
}
```

---

## 3. Event Contract Specifications (Asynchronous)

### Event Schema Definitions
```json
// Event Schema: {EventName}
{
  "event_id": "uuid",
  "event_type": "{EventType}",
  "event_version": "1.0",
  "timestamp": "ISO8601",
  "source_service": "{ServiceName}",
  "correlation_id": "uuid",
  "data": {
    // Event-specific payload
    "entity_id": "string",
    "entity_type": "string",
    "change_type": "created|updated|deleted",
    "previous_state": {},
    "current_state": {}
  },
  "metadata": {
    "tenant_id": "string",
    "user_id": "string",
    "session_id": "string"
  }
}
```

### Event Publishing Contract
- **Event Topic/Queue:** {topic_name}
- **Publishing Service:** {Service A}
- **Event Frequency:** {Expected frequency}
- **Retention Policy:** {How long events are retained}
- **Ordering Guarantees:** {Ordering requirements}

### Event Consumption Contract
- **Consuming Service:** {Service B}
- **Processing Guarantees:** {At-least-once, exactly-once, at-most-once}
- **Error Handling:** {Dead letter queue, retry policy}
- **Idempotency:** {How duplicate events are handled}

---

## 4. Data Consistency and Transaction Boundaries

### Consistency Model
- [ ] **Strong Consistency** - Immediate consistency across services
- [ ] **Eventual Consistency** - Consistency achieved over time
- [ ] **Causal Consistency** - Causally related operations are consistent

### Transaction Coordination
- [ ] **No Distributed Transactions** - Each service manages its own transactions
- [ ] **Saga Pattern** - Distributed transaction coordination with compensation
- [ ] **Two-Phase Commit** - Traditional distributed transaction (use sparingly)

### Compensation Actions
{If using Saga pattern, define compensation actions for rollback scenarios}

---

## 5. Performance and Reliability Requirements

### Performance Targets
- **Response Time:** {Target response time, e.g., < 200ms for 95th percentile}
- **Throughput:** {Expected requests per second}
- **Availability:** {Target uptime, e.g., 99.9%}

### Reliability Patterns
- [ ] **Circuit Breaker** - Fail fast when downstream service is unavailable
- [ ] **Retry with Backoff** - Retry failed requests with exponential backoff
- [ ] **Timeout Configuration** - Maximum wait time for responses
- [ ] **Bulkhead Pattern** - Isolate resources to prevent cascade failures

### Fallback Strategies
{Define what happens when the integration fails}
- **Graceful Degradation:** {How service continues with limited functionality}
- **Default Values:** {Default responses when service is unavailable}
- **Cached Responses:** {Use of cached data during outages}

---

## 6. Security and Authentication

### Authentication Method
- [ ] **Service-to-Service JWT** - JWT tokens for service authentication
- [ ] **mTLS** - Mutual TLS for secure communication
- [ ] **API Keys** - Simple API key authentication
- [ ] **OAuth 2.0** - OAuth for delegated authorization

### Authorization Rules
{Define what operations each service is authorized to perform}

### Data Protection
- [ ] **Encryption in Transit** - TLS/HTTPS for all communications
- [ ] **Encryption at Rest** - Sensitive data encrypted in storage
- [ ] **Data Masking** - PII and sensitive data properly masked
- [ ] **Audit Logging** - All interactions logged for security auditing

---

## 7. Monitoring and Observability

### Metrics to Track
- **Request Count:** Total number of requests
- **Response Time:** Request processing time
- **Error Rate:** Percentage of failed requests
- **Throughput:** Requests per second
- **Availability:** Service uptime percentage

### Distributed Tracing
- **Trace ID Propagation:** {How trace IDs are passed between services}
- **Span Creation:** {What operations create spans}
- **Trace Sampling:** {Sampling strategy for traces}

### Alerting Rules
- **High Error Rate:** Alert when error rate > {threshold}%
- **High Latency:** Alert when response time > {threshold}ms
- **Service Unavailable:** Alert when service is down

---

## 8. Testing Strategy

### Contract Testing
- [ ] **Consumer-Driven Contracts** - Consumer defines expected contract
- [ ] **Provider Contract Tests** - Provider validates contract compliance
- [ ] **Contract Evolution Tests** - Backward compatibility validation

### Integration Testing
- [ ] **End-to-End Tests** - Full workflow testing across services
- [ ] **Component Tests** - Service boundary testing
- [ ] **Chaos Testing** - Failure scenario testing

### Test Data Management
{How test data is managed across service boundaries}

---

## 9. Versioning and Evolution

### API Versioning Strategy
- [ ] **URL Versioning** - Version in URL path (/v1/, /v2/)
- [ ] **Header Versioning** - Version in HTTP headers
- [ ] **Content Negotiation** - Version in Accept/Content-Type headers

### Backward Compatibility
- **Breaking Changes:** {How breaking changes are handled}
- **Deprecation Policy:** {How long deprecated versions are supported}
- **Migration Strategy:** {How consumers migrate to new versions}

### Schema Evolution
- **Additive Changes:** {Adding new fields - should be non-breaking}
- **Field Removal:** {Removing fields - requires version bump}
- **Type Changes:** {Changing field types - requires careful planning}

---

## 10. Operational Procedures

### Deployment Coordination
- **Deployment Order:** {Which service should be deployed first}
- **Rollback Procedures:** {How to rollback if integration fails}
- **Blue-Green Deployment:** {How to handle zero-downtime deployments}

### Incident Response
- **Escalation Path:** {Who to contact when integration fails}
- **Troubleshooting Guide:** {Common issues and solutions}
- **Communication Protocol:** {How teams communicate during incidents}

### Maintenance Windows
- **Scheduled Maintenance:** {How maintenance is coordinated}
- **Impact Assessment:** {How to assess impact of changes}
- **Notification Procedures:** {How teams are notified of changes}

---

## 11. Acceptance Criteria

### Functional Acceptance
- [ ] All API endpoints respond correctly with expected data formats
- [ ] Event publishing and consumption work as specified
- [ ] Error handling behaves according to contract
- [ ] Authentication and authorization work correctly

### Non-Functional Acceptance
- [ ] Performance targets are met under expected load
- [ ] Reliability patterns function correctly during failures
- [ ] Security requirements are properly implemented
- [ ] Monitoring and alerting are operational

### Testing Acceptance
- [ ] All contract tests pass
- [ ] Integration tests cover happy path and error scenarios
- [ ] Performance tests validate SLA compliance
- [ ] Security tests validate protection measures

---

## 12. Sign-off and Approval

### Technical Review
- **Provider Team Lead:** {Name} - {Date} - {Signature}
- **Consumer Team Lead:** {Name} - {Date} - {Signature}
- **Architecture Review:** {Name} - {Date} - {Signature}

### Business Review
- **Product Owner (Provider):** {Name} - {Date} - {Signature}
- **Product Owner (Consumer):** {Name} - {Date} - {Signature}

### Operations Review
- **DevOps/SRE Lead:** {Name} - {Date} - {Signature}
- **Security Review:** {Name} - {Date} - {Signature}

---

## 13. Change Log

| Version | Date | Changes | Author | Approver |
|---------|------|---------|--------|----------|
| 1.0 | {Date} | Initial contract creation | {Author} | {Approver} |

---

## 14. Related Documents

- **Service A Documentation:** {Link to service documentation}
- **Service B Documentation:** {Link to service documentation}
- **System Architecture:** {Link to overall system architecture}
- **API Standards:** {Link to organizational API standards}
- **Security Guidelines:** {Link to security requirements}

==================== END: service-integration-contract-tmpl ====================


==================== START: story-tmpl ====================
# Story {EpicNum}.{StoryNum}: {Short Title Copied from Epic File}

## Status: { Draft | Approved | InProgress | Review | Done }

## Story

- As a [role]
- I want [action]
- so that [benefit]

## Acceptance Criteria (ACs)

{ Copy the Acceptance Criteria numbered list }

## Tasks / Subtasks

- [ ] Task 1 (AC: # if applicable)
  - [ ] Subtask1.1...
- [ ] Task 2 (AC: # if applicable)
  - [ ] Subtask 2.1...
- [ ] Task 3 (AC: # if applicable)
  - [ ] Subtask 3.1...

## Dev Technical Guidance {detail not covered in tasks/subtasks}

## Story Progress Notes

### Agent Model Used: `<Agent Model Name/Version>`

### Completion Notes List

{Any notes about implementation choices, difficulties, or follow-up needed}

### Change Log

==================== END: story-tmpl ====================


