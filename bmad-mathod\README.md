# The BMAD-Method 4.0 - Microservices & AI-Native Methodology

## Enterprise-Scale Microservices Development with Agentic AI Integration

BMAD 4.0 is a completely transformed methodology for enterprise-scale microservices development with integrated agentic AI capabilities. This methodology supports sophisticated distributed systems architecture, platform engineering excellence, and human-AI collaboration patterns.

### Quick Start: Choose Your Project Type

The methodology supports multiple project types through specialized AI agents. Start by selecting the appropriate workflow for your needs:

#### 1. **System-Level Microservices Architecture** (Most Common)
For designing comprehensive microservices ecosystems:
- **Analyst**: Domain & Service Boundary Analysis → AI Integration Strategy → Platform Engineering Assessment
- **Product Manager**: Master System PRD Creation → Cross-Service Coordination
- **Platform Engineer**: IDP Architecture Design → Developer Experience Optimization
- **AI Orchestration Specialist**: Multi-Agent System Design → Human-AI Collaboration Framework
- **Service Mesh Architect**: Service Mesh Architecture Design → Traffic Management Strategy

#### 2. **Individual Service Development**
For developing specific microservices within an existing ecosystem:
- **Product Manager**: Individual Service PRD Development → Service Integration Contracts
- **Service Mesh Architect**: Service Integration patterns and communication protocols

#### 3. **AI-Focused Projects**
For projects emphasizing agentic AI capabilities:
- **Analyst**: AI Integration Strategy Design
- **AI Orchestration Specialist**: Multi-Agent System Design → AI Governance and Ethics
- **Platform Engineer**: AI Platform Capabilities integration

#### 4. **Platform Engineering Projects**
For Internal Developer Platform development:
- **Analyst**: Platform Engineering Assessment
- **Platform Engineer**: IDP Architecture Design → Operational Excellence Framework
- **Product Manager**: Platform Engineering Strategy

### Web Quickstart Project Setup (Recommended)

Orchestrator Uber BMad Agent that does it all - already pre-compiled in the `web-build-sample` folder.

- The contents of [Agent Prompt Sample](web-build-sample/agent-prompt.txt) text get pasted into the Gemini Gem, or ChatPGT customGPT 'Instructions' field.
- The remaining files in that same folder folder just need to be attached as shown in the screenshot below. Give it a name (such as BMad Agent) and save it, and you now have the BMad Agent available to help you brainstorm, research, plan, execute on your vision, or understand how this all even works!
- Once its running, start with typing `/help`, and then type option `2` when it presents 3 options to learn about the method!

![image info](docs/images/gem-setup.png)

[More Documentation, Explanations, and IDE Specifics](docs/readme.md) available here!

## End Matter

Interested in improving the BMAD Method? See the [contributing guidelines](docs/CONTRIBUTING.md).

Thank you and enjoy - BMad!
[License](docs/LICENSE)
