# The BMAD-Method 4.0 - Microservices & AI-Native Methodology

## Enterprise-Scale Microservices Development with Agentic AI Integration

BMAD 4.0 is a completely transformed methodology for enterprise-scale microservices development with integrated agentic AI capabilities. This methodology supports sophisticated distributed systems architecture, platform engineering excellence, and human-AI collaboration patterns.

### Quick Start: Choose Your Project Type

The methodology supports multiple project types through specialized AI agents. Start by selecting the appropriate workflow for your needs:

#### 1. **System-Level Microservices Architecture** (Most Common)
For designing comprehensive microservices ecosystems:
- **Analyst**: Domain & Service Boundary Analysis → AI Integration Strategy → Platform Engineering Assessment
- **Product Manager**: Master System PRD Creation → Cross-Service Coordination
- **Platform Engineer**: IDP Architecture Design → Developer Experience Optimization
- **AI Orchestration Specialist**: Multi-Agent System Design → Human-AI Collaboration Framework
- **Service Mesh Architect**: Service Mesh Architecture Design → Traffic Management Strategy

#### 2. **Individual Service Development**
For developing specific microservices within an existing ecosystem:
- **Product Manager**: Individual Service PRD Development → Service Integration Contracts
- **Service Mesh Architect**: Service Integration patterns and communication protocols

#### 3. **AI-Focused Projects**
For projects emphasizing agentic AI capabilities:
- **Analyst**: AI Integration Strategy Design
- **AI Orchestration Specialist**: Multi-Agent System Design → AI Governance and Ethics
- **Platform Engineer**: AI Platform Capabilities integration

#### 4. **Platform Engineering Projects**
For Internal Developer Platform development:
- **Analyst**: Platform Engineering Assessment
- **Platform Engineer**: IDP Architecture Design → Operational Excellence Framework
- **Product Manager**: Platform Engineering Strategy

### Quick Setup (Automated)

**Option 1: Automated Setup (Recommended)**
```bash
# Clone and setup in one command
git clone <repository-url> bmad-method
cd bmad-method

# Run automated setup (choose your platform)
./setup.sh          # Unix/Linux/macOS
setup.ps1           # Windows PowerShell
setup.bat           # Windows Command Prompt

# Or use npm
npm run setup
```

**Option 2: Manual Setup**
```bash
# Install dependencies
npm install

# Build web agent bundle
npm run build

# Validate setup
npm test
```

### Web Quickstart Project Setup

After setup, your web agent bundle will be ready in the `./build/` directory:

- Copy the contents of `build/agent-prompt.txt` into your Gemini Gem or ChatGPT custom GPT 'Instructions' field
- Attach all the `.txt` files from the `build/` folder as knowledge files
- Give it a name (such as "BMAD Agent") and save it
- Start with typing `/help` to learn about the method!

![image info](docs/images/gem-setup.png)

### Available Commands

```bash
npm run build        # Build web agent bundles
npm run dev          # Clean and rebuild
npm run deploy:web   # Prepare for web deployment
npm test             # Validate configuration
npm run validate:agents  # Validate agent configurations
npm run clean        # Clean build directory
```

### Modern Microservices & Microfrontend Agents (New in 4.0)

BMAD 4.0 now includes specialized agents for enterprise-scale microservices and microfrontend architectures:

**Backend & Infrastructure Specialists:**
- **Service Mesh Architect (Alex)**: Traffic management, service communication, distributed systems security, and frontend service integration
- **Platform Engineer (Taylor)**: Internal Developer Platform design and developer experience optimization
- **AI Orchestration Specialist (Morgan)**: Multi-agent AI systems and human-AI collaboration frameworks

**Frontend & Microfrontend Specialists:**
- **Microfrontend Architect (Jordan)**: Microfrontend architecture, module federation, and distributed frontend systems
- **Design Architect (Jane)**: Enhanced with microfrontend design patterns, design system governance, and cross-microfrontend UX consistency

These agents provide comprehensive coverage for modern distributed systems, from backend microservices to frontend microfrontends, enabling teams to build scalable, maintainable enterprise applications.

### Development Environment

**Docker Support:**
```bash
# Development environment
docker-compose up bmad-dev

# Production build
docker-compose up bmad-build

# Run validation
docker-compose up bmad-validate
```

**Environment Configuration:**
Copy `.env.example` to `.env` and customize for your environment.

[More Documentation, Explanations, and IDE Specifics](docs/readme.md) available here!

## End Matter

Interested in improving the BMAD Method? See the [contributing guidelines](docs/CONTRIBUTING.md).

Thank you and enjoy - BMad!
[License](docs/LICENSE)
